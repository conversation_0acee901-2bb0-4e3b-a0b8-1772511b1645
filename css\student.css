/* Student Portal Specific Styles */

/* Portal Header */
.portal-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: var(--white);
  box-shadow: var(--shadow-lg);
}

.portal-header .logo h1 {
  color: var(--white);
  margin-bottom: var(--spacing-1);
}

.portal-type {
  font-size: var(--font-size-sm);
  opacity: 0.9;
}

.portal-nav {
  display: flex;
  gap: var(--spacing-6);
}

.portal-nav .nav-link {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.portal-nav .nav-link:hover,
.portal-nav .nav-link.active {
  color: var(--white);
  background-color: rgba(255, 255, 255, 0.1);
}

.user-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.user-avatar img {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.user-name {
  font-weight: 500;
  color: var(--white);
}

.logout-btn {
  background: rgba(255, 255, 255, 0.1);
  color: var(--white);
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Main Content */
.main-content {
  padding: var(--spacing-8) 0;
  min-height: calc(100vh - 80px);
}

/* Welcome Section */
.welcome-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-8);
  padding: var(--spacing-6);
  background: linear-gradient(135deg, var(--white), var(--gray-50));
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
}

.welcome-content h2 {
  font-size: var(--font-size-3xl);
  color: var(--gray-900);
  margin-bottom: var(--spacing-2);
}

.welcome-content p {
  color: var(--gray-600);
  font-size: var(--font-size-lg);
  margin-bottom: 0;
}

.quick-stats {
  display: flex;
  gap: var(--spacing-4);
}

.stat-card {
  text-align: center;
  padding: var(--spacing-4);
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  min-width: 100px;
}

.stat-number {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--spacing-1);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

/* Dashboard Grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-6);
}

.dashboard-card {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.dashboard-card h3 {
  font-size: var(--font-size-xl);
  color: var(--gray-900);
  margin-bottom: var(--spacing-4);
  border-bottom: 2px solid var(--gray-100);
  padding-bottom: var(--spacing-2);
}

/* Current Lesson Card */
.lesson-preview {
  display: flex;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.lesson-image {
  width: 80px;
  height: 80px;
  border-radius: var(--radius-lg);
  object-fit: cover;
}

.lesson-info {
  flex: 1;
}

.lesson-info h4 {
  font-size: var(--font-size-lg);
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
}

.lesson-info p {
  color: var(--gray-600);
  margin-bottom: var(--spacing-3);
}

.lesson-progress {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--radius-full);
  transition: width var(--transition-normal);
}

.lesson-progress span {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  font-weight: 500;
}

/* Upcoming Class Card */
.class-info {
  display: flex;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.class-time {
  text-align: center;
  padding: var(--spacing-3);
  background: var(--primary-color);
  color: var(--white);
  border-radius: var(--radius-lg);
  min-width: 100px;
}

.time {
  font-size: var(--font-size-xl);
  font-weight: 700;
  margin-bottom: var(--spacing-1);
}

.date {
  font-size: var(--font-size-sm);
  opacity: 0.9;
}

.class-details h4 {
  font-size: var(--font-size-lg);
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
}

.class-details p {
  color: var(--gray-600);
  margin-bottom: var(--spacing-1);
  font-size: var(--font-size-sm);
}

/* AI Tutor Card */
.ai-preview {
  display: flex;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.ai-avatar img {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-full);
  border: 3px solid var(--accent-color);
}

.ai-message {
  flex: 1;
  background: var(--gray-50);
  padding: var(--spacing-3);
  border-radius: var(--radius-lg);
  position: relative;
}

.ai-message::before {
  content: '';
  position: absolute;
  left: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 8px solid var(--gray-50);
}

.ai-message p {
  margin: 0;
  color: var(--gray-700);
  font-style: italic;
}

/* Recent Progress Card */
.achievements-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.achievement {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
}

.achievement-icon {
  font-size: var(--font-size-xl);
  width: 40px;
  text-align: center;
}

.achievement-text h4 {
  font-size: var(--font-size-base);
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
}

.achievement-text p {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  margin: 0;
}

/* Quick Actions Card */
.actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-3);
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-4);
  background: var(--gray-50);
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  color: var(--gray-700);
}

.action-btn:hover {
  background: var(--primary-color);
  color: var(--white);
  transform: translateY(-2px);
}

.action-icon {
  font-size: var(--font-size-xl);
}

.action-btn span:last-child {
  font-size: var(--font-size-sm);
  font-weight: 500;
}

/* Study Streak Card */
.streak-display {
  text-align: center;
  margin-bottom: var(--spacing-4);
}

.streak-number {
  font-size: var(--font-size-5xl);
  font-weight: 700;
  color: var(--accent-color);
  line-height: 1;
}

.streak-label {
  font-size: var(--font-size-lg);
  color: var(--gray-600);
  margin-top: var(--spacing-1);
}

.study-streak p {
  text-align: center;
  color: var(--gray-600);
  margin-bottom: var(--spacing-4);
}

.streak-calendar {
  display: flex;
  justify-content: center;
  gap: var(--spacing-2);
}

.day {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  font-weight: 600;
  background: var(--gray-200);
  color: var(--gray-600);
}

.day.completed {
  background: var(--secondary-color);
  color: var(--white);
}

/* Level Progress Section */
.level-progress-section {
  margin-bottom: var(--spacing-8);
}

.level-progress-card {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--white);
  position: relative;
  overflow: hidden;
}

.level-progress-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: url('https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80') center/cover;
  opacity: 0.1;
  border-radius: 50%;
  transform: translate(50%, -50%);
}

.level-progress-card h3 {
  color: var(--white);
  margin-bottom: var(--spacing-6);
}

.level-progress-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-6);
  position: relative;
  z-index: 1;
}

.level-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);
}

.level-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: var(--font-size-lg);
  transition: all var(--transition-normal);
  border: 3px solid transparent;
}

.level-item.completed .level-circle {
  background: var(--white);
  color: var(--primary-color);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
}

.level-item.current .level-circle {
  background: var(--accent-color);
  color: var(--white);
  border-color: var(--white);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
  animation: pulse 2s infinite;
}

.level-item.locked .level-circle {
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.6);
}

.level-label {
  font-size: var(--font-size-sm);
  font-weight: 500;
  text-align: center;
  opacity: 0.9;
}

.level-connector {
  flex: 1;
  height: 3px;
  background: rgba(255, 255, 255, 0.3);
  margin: 0 var(--spacing-2);
  position: relative;
  top: -15px;
}

.level-connector.completed {
  background: var(--white);
}

.current-level-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  padding: var(--spacing-4);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
}

.level-info h4 {
  color: var(--white);
  margin-bottom: var(--spacing-1);
}

.level-info p {
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

.level-stats {
  display: flex;
  gap: var(--spacing-4);
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--white);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: rgba(255, 255, 255, 0.8);
}

/* Skills Progress */
.skills-progress-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.skill-item {
  background: var(--gray-50);
  padding: var(--spacing-3);
  border-radius: var(--radius-lg);
}

.skill-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2);
}

.skill-name {
  font-weight: 600;
  color: var(--gray-900);
}

.skill-score {
  font-weight: 700;
  color: var(--primary-color);
}

.skill-progress-bar {
  height: 8px;
  background: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.skill-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--radius-full);
  transition: width 1s ease-out;
}

.skills-summary {
  margin-top: var(--spacing-4);
  padding: var(--spacing-3);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
  border-left: 4px solid var(--accent-color);
}

.skills-summary p {
  margin: 0;
  color: var(--gray-700);
  font-size: var(--font-size-sm);
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Button Styles */
.btn-accent {
  background: linear-gradient(135deg, var(--accent-color), var(--accent-light));
  color: var(--white);
}

.btn-accent:hover {
  background: linear-gradient(135deg, var(--accent-light), var(--accent-color));
  color: var(--white);
}

/* Responsive Design */
@media (max-width: 768px) {
  .welcome-section {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-4);
  }

  .quick-stats {
    justify-content: center;
    flex-wrap: wrap;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .portal-nav {
    display: none;
  }

  .user-menu {
    gap: var(--spacing-2);
  }

  .user-name {
    display: none;
  }

  /* Level Progress Mobile */
  .level-progress-container {
    flex-wrap: wrap;
    gap: var(--spacing-3);
  }

  .level-connector {
    display: none;
  }

  .level-circle {
    width: 50px;
    height: 50px;
    font-size: var(--font-size-base);
  }

  .current-level-details {
    flex-direction: column;
    gap: var(--spacing-3);
    text-align: center;
  }

  .level-stats {
    justify-content: center;
  }

  /* Skills Progress Mobile */
  .skills-progress-container {
    gap: var(--spacing-3);
  }

  .skill-item {
    padding: var(--spacing-2);
  }
}

@media (max-width: 480px) {
  .lesson-preview,
  .class-info,
  .ai-preview {
    flex-direction: column;
    text-align: center;
  }

  .actions-grid {
    grid-template-columns: 1fr;
  }

  /* Compact level progress for small screens */
  .level-progress-container {
    justify-content: center;
  }

  .level-circle {
    width: 45px;
    height: 45px;
    font-size: var(--font-size-sm);
  }

  .level-label {
    font-size: var(--font-size-xs);
  }

  .stat-item {
    min-width: 80px;
  }

  .stat-value {
    font-size: var(--font-size-lg);
  }

  .skill-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-1);
  }
}

/* Enhanced hover effects for desktop */
@media (min-width: 769px) {
  .level-item:hover .level-circle {
    transform: scale(1.1);
  }

  .skill-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }

  .dashboard-card:hover .skill-progress-fill {
    animation: progressGlow 1s ease-in-out;
  }
}

@keyframes progressGlow {
  0%, 100% {
    box-shadow: none;
  }
  50% {
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
  }
}

/* Progress Tracker Component Styles */
.progress-tracker {
  display: grid;
  gap: var(--spacing-6);
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.progress-section {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.progress-section:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.progress-section h3 {
  margin-bottom: var(--spacing-4);
  color: var(--gray-900);
  font-size: var(--font-size-xl);
  font-weight: 600;
}

/* Circular Progress */
.circular-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-4);
}

.progress-circle {
  position: relative;
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-ring {
  transform: rotate(-90deg);
  position: absolute;
  top: 0;
  left: 0;
}

.progress-ring-background {
  fill: none;
  stroke: var(--gray-200);
  stroke-width: 8;
}

.progress-ring-fill {
  fill: none;
  stroke: var(--primary-color);
  stroke-width: 8;
  stroke-linecap: round;
  transition: stroke-dashoffset 2s ease-out;
}

.progress-text {
  text-align: center;
  z-index: 1;
}

.progress-value {
  display: block;
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--primary-color);
}

.progress-label {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.progress-stats {
  display: flex;
  gap: var(--spacing-6);
  justify-content: center;
}

.stat {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--gray-900);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

/* Level Timeline */
.level-timeline {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  padding: var(--spacing-4) 0;
}

.level-timeline::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gray-200);
  z-index: 1;
}

.level-milestone {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);
  z-index: 2;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.5s ease-out;
}

.level-milestone.animated {
  opacity: 1;
  transform: translateY(0);
}

.milestone-marker {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: var(--font-size-base);
  background: var(--white);
  border: 3px solid var(--gray-300);
  transition: all var(--transition-normal);
}

.level-milestone.completed .milestone-marker {
  background: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.level-milestone.current .milestone-marker {
  background: var(--accent-color);
  color: var(--white);
  border-color: var(--accent-color);
  animation: pulse 2s infinite;
}

.milestone-label {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  text-align: center;
  font-weight: 500;
}

/* Skills Grid */
.skills-grid {
  display: grid;
  gap: var(--spacing-4);
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.skill-card {
  background: var(--gray-50);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
  transition: all var(--transition-normal);
}

.skill-card:hover {
  background: var(--gray-100);
  transform: translateY(-2px);
}

.skill-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-3);
}

.skill-name {
  font-weight: 600;
  color: var(--gray-900);
}

.skill-score {
  font-weight: 700;
  color: var(--primary-color);
}

.skill-bar {
  height: 8px;
  background: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-bottom: var(--spacing-2);
}

.skill-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--radius-full);
  width: 0%;
  transition: width 1.5s ease-out;
}

.skill-level {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  text-align: center;
  font-weight: 500;
}

/* Achievements */
.achievements-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
  border-left: 4px solid var(--primary-color);
  transition: all var(--transition-normal);
}

.achievement-item:hover {
  background: var(--gray-100);
  transform: translateX(4px);
}

.achievement-item.milestone {
  border-left-color: var(--accent-color);
}

.achievement-item.streak {
  border-left-color: var(--secondary-color);
}

.achievement-icon {
  font-size: var(--font-size-2xl);
  width: 40px;
  text-align: center;
}

.achievement-content {
  flex: 1;
}

.achievement-title {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
}

.achievement-description {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.achievement-date {
  font-size: var(--font-size-xs);
  color: var(--gray-500);
  font-weight: 500;
}
