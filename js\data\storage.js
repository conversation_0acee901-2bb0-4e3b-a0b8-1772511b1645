/**
 * ENGLISH 2025 - Storage Manager
 * Handles LocalStorage operations, data validation, and sanitization
 */

class StorageManager {
    constructor() {
        this.prefix = 'esl_';
        this.version = '1.0';
        this.init();
    }

    /**
     * Initialize storage manager
     */
    init() {
        this.validateStorageSupport();
        this.migrateDataIfNeeded();
    }

    /**
     * Check if localStorage is supported
     */
    validateStorageSupport() {
        try {
            const testKey = this.prefix + 'test';
            localStorage.setItem(testKey, 'test');
            localStorage.removeItem(testKey);
            return true;
        } catch (error) {
            console.error('LocalStorage not supported:', error);
            throw new Error('LocalStorage is required for this application');
        }
    }

    /**
     * Migrate data if version changes
     */
    migrateDataIfNeeded() {
        const currentVersion = this.get('app_version');
        if (currentVersion !== this.version) {
            console.log(`Migrating data from version ${currentVersion} to ${this.version}`);
            this.set('app_version', this.version);
        }
    }

    /**
     * Set data in localStorage with validation
     * @param {string} key - Storage key
     * @param {*} value - Value to store
     * @param {Object} options - Storage options
     * @returns {boolean} Success status
     */
    set(key, value, options = {}) {
        try {
            const fullKey = this.prefix + key;
            const data = {
                value: this.sanitizeData(value),
                timestamp: new Date().toISOString(),
                version: this.version,
                ...options
            };

            // Check storage quota
            const serialized = JSON.stringify(data);
            if (this.checkStorageQuota(serialized)) {
                localStorage.setItem(fullKey, serialized);
                return true;
            } else {
                console.warn('Storage quota exceeded for key:', key);
                return false;
            }
        } catch (error) {
            console.error('Error setting storage data:', error);
            return false;
        }
    }

    /**
     * Get data from localStorage with validation
     * @param {string} key - Storage key
     * @param {*} defaultValue - Default value if key doesn't exist
     * @returns {*} Retrieved value or default
     */
    get(key, defaultValue = null) {
        try {
            const fullKey = this.prefix + key;
            const stored = localStorage.getItem(fullKey);
            
            if (stored === null) {
                return defaultValue;
            }

            const data = JSON.parse(stored);
            
            // Check if data has expired
            if (data.expiresAt && new Date() > new Date(data.expiresAt)) {
                this.remove(key);
                return defaultValue;
            }

            return data.value;
        } catch (error) {
            console.error('Error getting storage data:', error);
            return defaultValue;
        }
    }

    /**
     * Remove data from localStorage
     * @param {string} key - Storage key
     * @returns {boolean} Success status
     */
    remove(key) {
        try {
            const fullKey = this.prefix + key;
            localStorage.removeItem(fullKey);
            return true;
        } catch (error) {
            console.error('Error removing storage data:', error);
            return false;
        }
    }

    /**
     * Clear all application data
     * @param {boolean} keepUserData - Whether to keep user data
     * @returns {boolean} Success status
     */
    clear(keepUserData = false) {
        try {
            const keysToKeep = keepUserData ? ['users_data', 'user_session'] : [];
            const keys = Object.keys(localStorage);
            
            keys.forEach(key => {
                if (key.startsWith(this.prefix)) {
                    const shortKey = key.replace(this.prefix, '');
                    if (!keysToKeep.includes(shortKey)) {
                        localStorage.removeItem(key);
                    }
                }
            });
            
            return true;
        } catch (error) {
            console.error('Error clearing storage:', error);
            return false;
        }
    }

    /**
     * Get all keys with prefix
     * @returns {Array} Array of keys
     */
    getAllKeys() {
        const keys = Object.keys(localStorage);
        return keys
            .filter(key => key.startsWith(this.prefix))
            .map(key => key.replace(this.prefix, ''));
    }

    /**
     * Get storage usage information
     * @returns {Object} Storage usage stats
     */
    getStorageInfo() {
        try {
            let totalSize = 0;
            let appSize = 0;
            const keys = Object.keys(localStorage);
            
            keys.forEach(key => {
                const value = localStorage.getItem(key);
                const size = new Blob([value]).size;
                totalSize += size;
                
                if (key.startsWith(this.prefix)) {
                    appSize += size;
                }
            });

            return {
                totalSize,
                appSize,
                totalKeys: keys.length,
                appKeys: this.getAllKeys().length,
                quota: this.getStorageQuota()
            };
        } catch (error) {
            console.error('Error getting storage info:', error);
            return null;
        }
    }

    /**
     * Check if storage quota allows for new data
     * @param {string} data - Data to check
     * @returns {boolean} Whether data fits in quota
     */
    checkStorageQuota(data) {
        try {
            const dataSize = new Blob([data]).size;
            const quota = this.getStorageQuota();
            const currentUsage = this.getStorageInfo().totalSize;
            
            return (currentUsage + dataSize) < quota * 0.9; // Use 90% of quota
        } catch (error) {
            console.error('Error checking storage quota:', error);
            return false;
        }
    }

    /**
     * Get estimated storage quota
     * @returns {number} Estimated quota in bytes
     */
    getStorageQuota() {
        // Most browsers have 5-10MB for localStorage
        return 5 * 1024 * 1024; // 5MB default estimate
    }

    /**
     * Sanitize data before storage
     * @param {*} data - Data to sanitize
     * @returns {*} Sanitized data
     */
    sanitizeData(data) {
        if (typeof data === 'string') {
            // Remove potentially harmful scripts
            return data.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
        }
        
        if (typeof data === 'object' && data !== null) {
            if (Array.isArray(data)) {
                return data.map(item => this.sanitizeData(item));
            } else {
                const sanitized = {};
                for (const key in data) {
                    if (data.hasOwnProperty(key)) {
                        sanitized[key] = this.sanitizeData(data[key]);
                    }
                }
                return sanitized;
            }
        }
        
        return data;
    }

    /**
     * Validate data structure
     * @param {*} data - Data to validate
     * @param {Object} schema - Validation schema
     * @returns {Object} Validation result
     */
    validateData(data, schema) {
        const errors = [];
        
        try {
            if (schema.required) {
                schema.required.forEach(field => {
                    if (!(field in data)) {
                        errors.push(`Required field '${field}' is missing`);
                    }
                });
            }
            
            if (schema.fields) {
                for (const field in schema.fields) {
                    if (field in data) {
                        const fieldSchema = schema.fields[field];
                        const value = data[field];
                        
                        if (fieldSchema.type && typeof value !== fieldSchema.type) {
                            errors.push(`Field '${field}' must be of type ${fieldSchema.type}`);
                        }
                        
                        if (fieldSchema.minLength && value.length < fieldSchema.minLength) {
                            errors.push(`Field '${field}' must be at least ${fieldSchema.minLength} characters`);
                        }
                        
                        if (fieldSchema.maxLength && value.length > fieldSchema.maxLength) {
                            errors.push(`Field '${field}' must be at most ${fieldSchema.maxLength} characters`);
                        }
                        
                        if (fieldSchema.pattern && !fieldSchema.pattern.test(value)) {
                            errors.push(`Field '${field}' format is invalid`);
                        }
                    }
                }
            }
        } catch (error) {
            errors.push('Validation error: ' + error.message);
        }
        
        return {
            valid: errors.length === 0,
            errors
        };
    }

    /**
     * Get validation schema for different data types
     * @param {string} dataType - Type of data to validate
     * @returns {Object} Validation schema
     */
    getValidationSchema(dataType) {
        const schemas = {
            student: {
                required: ['id', 'username', 'email', 'name', 'role', 'currentLevel'],
                fields: {
                    id: { type: 'string', minLength: 1 },
                    username: { type: 'string', minLength: 3, maxLength: 50, pattern: /^[a-zA-Z0-9._-]+$/ },
                    email: { type: 'string', pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ },
                    name: { type: 'string', minLength: 1, maxLength: 100 },
                    role: { type: 'string', enum: ['student'] },
                    currentLevel: { type: 'string', enum: ['A1', 'A2', 'B1', 'B2', 'C1'] },
                    subscriptionType: { type: 'string', enum: ['8', '12', '20'] }
                }
            },
            teacher: {
                required: ['id', 'username', 'email', 'name', 'role', 'specialization'],
                fields: {
                    id: { type: 'string', minLength: 1 },
                    username: { type: 'string', minLength: 3, maxLength: 50, pattern: /^[a-zA-Z0-9._-]+$/ },
                    email: { type: 'string', pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ },
                    name: { type: 'string', minLength: 1, maxLength: 100 },
                    role: { type: 'string', enum: ['teacher'] },
                    specialization: { type: 'string', minLength: 1 },
                    rating: { type: 'number', min: 0, max: 5 }
                }
            },
            admin: {
                required: ['id', 'username', 'email', 'name', 'role', 'title'],
                fields: {
                    id: { type: 'string', minLength: 1 },
                    username: { type: 'string', minLength: 3, maxLength: 50, pattern: /^[a-zA-Z0-9._-]+$/ },
                    email: { type: 'string', pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ },
                    name: { type: 'string', minLength: 1, maxLength: 100 },
                    role: { type: 'string', enum: ['admin'] },
                    title: { type: 'string', minLength: 1 }
                }
            },
            lesson: {
                required: ['id', 'title', 'level', 'unit', 'duration'],
                fields: {
                    id: { type: 'string', minLength: 1 },
                    title: { type: 'string', minLength: 1, maxLength: 200 },
                    level: { type: 'string', enum: ['A1', 'A2', 'B1', 'B2', 'C1'] },
                    unit: { type: 'string', minLength: 1 },
                    duration: { type: 'number', min: 1, max: 180 }
                }
            },
            payment: {
                required: ['id', 'studentId', 'amount', 'currency', 'status'],
                fields: {
                    id: { type: 'string', minLength: 1 },
                    studentId: { type: 'string', minLength: 1 },
                    amount: { type: 'number', min: 0 },
                    currency: { type: 'string', enum: ['USD', 'EUR', 'GBP'] },
                    status: { type: 'string', enum: ['pending', 'completed', 'failed', 'overdue', 'refunded'] }
                }
            }
        };

        return schemas[dataType] || {};
    }

    /**
     * Enhanced data validation with schema support
     * @param {*} data - Data to validate
     * @param {string} dataType - Type of data for schema selection
     * @returns {Object} Validation result
     */
    validateWithSchema(data, dataType) {
        const schema = this.getValidationSchema(dataType);
        if (!schema || Object.keys(schema).length === 0) {
            return { valid: true, errors: [] };
        }

        return this.validateData(data, schema);
    }

    /**
     * Sanitize and validate data before storage
     * @param {*} data - Data to process
     * @param {string} dataType - Type of data
     * @returns {Object} Processing result
     */
    processData(data, dataType) {
        try {
            // First sanitize the data
            const sanitizedData = this.sanitizeData(data);

            // Then validate it
            const validation = this.validateWithSchema(sanitizedData, dataType);

            return {
                success: validation.valid,
                data: sanitizedData,
                errors: validation.errors
            };
        } catch (error) {
            return {
                success: false,
                data: null,
                errors: ['Data processing failed: ' + error.message]
            };
        }
    }

    /**
     * Backup data to JSON
     * @returns {string} JSON backup string
     */
    exportData() {
        try {
            const data = {};
            this.getAllKeys().forEach(key => {
                data[key] = this.get(key);
            });
            
            return JSON.stringify({
                version: this.version,
                timestamp: new Date().toISOString(),
                data
            }, null, 2);
        } catch (error) {
            console.error('Error exporting data:', error);
            return null;
        }
    }

    /**
     * Restore data from JSON backup
     * @param {string} jsonData - JSON backup string
     * @returns {boolean} Success status
     */
    importData(jsonData) {
        try {
            const backup = JSON.parse(jsonData);
            
            if (!backup.data || !backup.version) {
                throw new Error('Invalid backup format');
            }
            
            // Clear existing data
            this.clear();
            
            // Restore data
            for (const key in backup.data) {
                this.set(key, backup.data[key]);
            }
            
            return true;
        } catch (error) {
            console.error('Error importing data:', error);
            return false;
        }
    }

    /**
     * Clean up expired data
     * @returns {number} Number of items cleaned
     */
    cleanup() {
        let cleaned = 0;
        try {
            const keys = this.getAllKeys();
            keys.forEach(key => {
                const data = this.get(key);
                if (data === null) { // This means it was expired and removed
                    cleaned++;
                }
            });
        } catch (error) {
            console.error('Error during cleanup:', error);
        }
        return cleaned;
    }

    /**
     * Set data with automatic validation
     * @param {string} key - Storage key
     * @param {*} value - Value to store
     * @param {string} dataType - Type of data for validation
     * @param {Object} options - Storage options
     * @returns {Object} Operation result
     */
    setWithValidation(key, value, dataType, options = {}) {
        const processed = this.processData(value, dataType);

        if (!processed.success) {
            return {
                success: false,
                errors: processed.errors
            };
        }

        const stored = this.set(key, processed.data, options);
        return {
            success: stored,
            errors: stored ? [] : ['Failed to store data']
        };
    }

    /**
     * Batch operations for multiple data items
     * @param {Array} operations - Array of {action, key, value, dataType} objects
     * @returns {Object} Batch operation result
     */
    batch(operations) {
        const results = [];
        let successCount = 0;
        let errorCount = 0;

        operations.forEach((op, index) => {
            try {
                let result;
                switch (op.action) {
                    case 'set':
                        if (op.dataType) {
                            result = this.setWithValidation(op.key, op.value, op.dataType, op.options);
                        } else {
                            result = { success: this.set(op.key, op.value, op.options), errors: [] };
                        }
                        break;
                    case 'get':
                        const value = this.get(op.key, op.defaultValue);
                        result = { success: true, value, errors: [] };
                        break;
                    case 'remove':
                        result = { success: this.remove(op.key), errors: [] };
                        break;
                    default:
                        result = { success: false, errors: [`Unknown action: ${op.action}`] };
                }

                results.push({ index, ...result });
                if (result.success) successCount++;
                else errorCount++;

            } catch (error) {
                results.push({
                    index,
                    success: false,
                    errors: [error.message]
                });
                errorCount++;
            }
        });

        return {
            success: errorCount === 0,
            totalOperations: operations.length,
            successCount,
            errorCount,
            results
        };
    }

    /**
     * Search stored data by pattern
     * @param {string} pattern - Search pattern (supports wildcards)
     * @param {string} searchIn - 'keys' or 'values'
     * @returns {Array} Search results
     */
    search(pattern, searchIn = 'keys') {
        const results = [];
        const regex = new RegExp(pattern.replace(/\*/g, '.*'), 'i');

        try {
            const keys = this.getAllKeys();

            keys.forEach(key => {
                if (searchIn === 'keys' && regex.test(key)) {
                    results.push({ key, value: this.get(key) });
                } else if (searchIn === 'values') {
                    const value = this.get(key);
                    const valueStr = JSON.stringify(value);
                    if (regex.test(valueStr)) {
                        results.push({ key, value });
                    }
                }
            });
        } catch (error) {
            console.error('Error during search:', error);
        }

        return results;
    }

    /**
     * Get storage statistics
     * @returns {Object} Detailed storage statistics
     */
    getDetailedStats() {
        try {
            const info = this.getStorageInfo();
            const keys = this.getAllKeys();
            const dataTypes = {};
            let totalItems = 0;

            keys.forEach(key => {
                const value = this.get(key);
                if (value !== null) {
                    totalItems++;
                    const type = Array.isArray(value) ? 'array' : typeof value;
                    dataTypes[type] = (dataTypes[type] || 0) + 1;
                }
            });

            return {
                ...info,
                totalItems,
                dataTypes,
                averageItemSize: totalItems > 0 ? Math.round(info.appSize / totalItems) : 0,
                storageUtilization: Math.round((info.appSize / info.quota) * 100)
            };
        } catch (error) {
            console.error('Error getting detailed stats:', error);
            return null;
        }
    }
}

// Create global instance
window.storageManager = new StorageManager();
