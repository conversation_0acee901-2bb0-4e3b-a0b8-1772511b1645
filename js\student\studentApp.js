/**
 * ENGLISH 2025 - Student Portal Application
 * Handles student-specific functionality and dashboard interactions
 */

class StudentApp {
    constructor() {
        this.authManager = window.authManager;
        this.currentUser = null;
        this.init();
    }

    /**
     * Initialize the student application
     */
    init() {
        this.checkAuthentication();
        this.setupEventListeners();
        this.loadDashboardData();
        this.animateOnLoad();
    }

    /**
     * Check if user is authenticated and has student role
     */
    checkAuthentication() {
        if (!this.authManager || !this.authManager.isAuthenticated()) {
            console.warn('No authentication found, redirecting to login');
            window.location.href = '../index.html';
            return;
        }

        if (!this.authManager.hasRole('student')) {
            console.warn('Unauthorized access to student portal - wrong role');
            window.location.href = '../index.html';
            return;
        }

        this.currentUser = this.authManager.getCurrentUser();
        if (!this.currentUser) {
            console.warn('Failed to get current user');
            window.location.href = '../index.html';
            return;
        }

        // Update UI with user info
        this.updateUserInterface();
    }

    /**
     * Update UI elements with user information
     */
    updateUserInterface() {
        if (!this.currentUser) return;

        // Update user name in header
        const userNameElements = document.querySelectorAll('.user-name');
        userNameElements.forEach(element => {
            element.textContent = this.currentUser.name;
        });

        // Update user email
        const userEmailElements = document.querySelectorAll('.user-email');
        userEmailElements.forEach(element => {
            element.textContent = this.currentUser.email;
        });

        // Update current level
        const levelElements = document.querySelectorAll('.current-level');
        levelElements.forEach(element => {
            element.textContent = this.currentUser.currentLevel;
        });

        // Update progress information
        if (this.currentUser.progress) {
            const progressElements = document.querySelectorAll('.overall-progress');
            progressElements.forEach(element => {
                element.textContent = `${this.currentUser.progress.overallProgress}%`;
            });
        }

        // Update study streak
        if (this.currentUser.studyStreak) {
            const streakElements = document.querySelectorAll('.study-streak');
            streakElements.forEach(element => {
                element.textContent = this.currentUser.studyStreak;
            });
        }
    }

    /**
     * Get current user information (legacy method for compatibility)
     */
    getCurrentUser() {
        return this.currentUser;
    }

    /**
     * Set up event listeners for student portal interactions
     */
    setupEventListeners() {
        // Continue lesson button
        const continueBtn = document.querySelector('.current-lesson .btn-primary');
        if (continueBtn) {
            continueBtn.addEventListener('click', () => {
                this.continueLesson();
            });
        }

        // Join class button
        const joinClassBtn = document.querySelector('.upcoming-class .btn-secondary');
        if (joinClassBtn) {
            joinClassBtn.addEventListener('click', () => {
                this.joinClass();
            });
        }

        // AI Tutor button
        const aiTutorBtn = document.querySelector('.ai-tutor .btn-accent');
        if (aiTutorBtn) {
            aiTutorBtn.addEventListener('click', () => {
                this.startAITutor();
            });
        }

        // Quick action buttons
        const actionButtons = document.querySelectorAll('.action-btn');
        actionButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const actionText = e.target.textContent || e.target.querySelector('span:last-child').textContent;
                this.handleQuickAction(actionText.trim());
            });
        });

        // Navigation links
        const navLinks = document.querySelectorAll('.portal-nav .nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleNavigation(link.getAttribute('href'));
            });
        });
    }

    /**
     * Load and display dashboard data
     */
    loadDashboardData() {
        this.updateWelcomeMessage();
        this.updateProgressStats();
        this.updateCurrentLesson();
        this.updateUpcomingClass();
        this.updateStudyStreak();
        this.updateRecentAchievements();
    }

    /**
     * Update welcome message with user name and level
     */
    updateWelcomeMessage() {
        const welcomeTitle = document.querySelector('.welcome-content h2');
        const welcomeText = document.querySelector('.welcome-content p');
        
        if (welcomeTitle) {
            welcomeTitle.textContent = `Welcome back, ${this.currentUser.name.split(' ')[0]}!`;
        }
        
        if (welcomeText) {
            welcomeText.innerHTML = `Continue your English learning journey. You're currently at <strong>Level ${this.currentUser.currentLevel}</strong>.`;
        }
    }

    /**
     * Update progress statistics with enhanced data
     */
    updateProgressStats() {
        const progressStat = document.querySelector('.stat-card .stat-number');
        const lessonsStat = document.querySelectorAll('.stat-card .stat-number')[1];
        const classesStat = document.querySelectorAll('.stat-card .stat-number')[2];

        if (progressStat) {
            progressStat.textContent = `${this.currentUser.progress.overallProgress}%`;
        }

        if (lessonsStat) {
            lessonsStat.textContent = this.currentUser.progress.completedLessons.length;
        }

        if (classesStat) {
            classesStat.textContent = this.currentUser.classesAttended || '8';
        }

        // Update level progress visualization
        this.updateLevelProgress();

        // Update skills assessment visualization
        this.updateSkillsProgress();
    }

    /**
     * Update level progress visualization
     */
    updateLevelProgress() {
        const levelProgressContainer = document.querySelector('.level-progress-container');
        if (!levelProgressContainer) return;

        const levels = ['A1', 'A2', 'B1', 'B2', 'C1'];
        const currentLevelIndex = levels.indexOf(this.currentUser.currentLevel);

        levelProgressContainer.innerHTML = '';

        levels.forEach((level, index) => {
            const levelItem = document.createElement('div');
            levelItem.className = 'level-item';

            if (index < currentLevelIndex) {
                levelItem.classList.add('completed');
            } else if (index === currentLevelIndex) {
                levelItem.classList.add('current');
            } else {
                levelItem.classList.add('locked');
            }

            levelItem.innerHTML = `
                <div class="level-circle">
                    <span class="level-text">${level}</span>
                </div>
                <div class="level-label">${this.getLevelName(level)}</div>
            `;

            levelProgressContainer.appendChild(levelItem);

            // Add connector line (except for last item)
            if (index < levels.length - 1) {
                const connector = document.createElement('div');
                connector.className = 'level-connector';
                if (index < currentLevelIndex) {
                    connector.classList.add('completed');
                }
                levelProgressContainer.appendChild(connector);
            }
        });
    }

    /**
     * Update skills progress visualization
     */
    updateSkillsProgress() {
        const skillsContainer = document.querySelector('.skills-progress-container');
        if (!skillsContainer || !this.currentUser.progress.skillsAssessment) return;

        const skills = this.currentUser.progress.skillsAssessment;
        skillsContainer.innerHTML = '';

        Object.entries(skills).forEach(([skill, score]) => {
            const skillItem = document.createElement('div');
            skillItem.className = 'skill-item';

            skillItem.innerHTML = `
                <div class="skill-header">
                    <span class="skill-name">${this.capitalizeFirst(skill)}</span>
                    <span class="skill-score">${score}%</span>
                </div>
                <div class="skill-progress-bar">
                    <div class="skill-progress-fill" style="width: ${score}%"></div>
                </div>
            `;

            skillsContainer.appendChild(skillItem);
        });
    }

    /**
     * Get level name from code
     */
    getLevelName(levelCode) {
        const levelNames = {
            'A1': 'Beginner',
            'A2': 'Elementary',
            'B1': 'Intermediate',
            'B2': 'Upper Int.',
            'C1': 'Advanced'
        };
        return levelNames[levelCode] || levelCode;
    }

    /**
     * Capitalize first letter
     */
    capitalizeFirst(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    }

    /**
     * Update current lesson information with real data
     */
    updateCurrentLesson() {
        const progressFill = document.querySelector('.lesson-progress .progress-fill');
        const progressText = document.querySelector('.lesson-progress span');
        const lessonTitle = document.querySelector('.lesson-info h4');
        const lessonSubtitle = document.querySelector('.lesson-info p');

        // Calculate current lesson progress based on user data
        const currentLessonProgress = this.calculateCurrentLessonProgress();

        if (progressFill) {
            progressFill.style.width = `${currentLessonProgress}%`;
        }

        if (progressText) {
            progressText.textContent = `${currentLessonProgress}% Complete`;
        }

        // Update lesson information based on current progress
        const currentLessonInfo = this.getCurrentLessonInfo();
        if (lessonTitle) {
            lessonTitle.textContent = currentLessonInfo.unit;
        }

        if (lessonSubtitle) {
            lessonSubtitle.textContent = currentLessonInfo.lesson;
        }
    }

    /**
     * Calculate current lesson progress
     */
    calculateCurrentLessonProgress() {
        // Mock calculation - in real app this would be based on actual lesson data
        const completedLessons = this.currentUser.progress.completedLessons.length;
        const totalLessonsInLevel = 16; // 4 units × 4 lessons
        const progressInCurrentLevel = (completedLessons % totalLessonsInLevel) / totalLessonsInLevel;
        return Math.round(progressInCurrentLevel * 100);
    }

    /**
     * Get current lesson information
     */
    getCurrentLessonInfo() {
        const completedLessons = this.currentUser.progress.completedLessons.length;

        // Calculate current unit and lesson
        const lessonsInCurrentLevel = completedLessons % 16;
        const currentUnit = Math.floor(lessonsInCurrentLevel / 4) + 1;
        const currentLesson = (lessonsInCurrentLevel % 4) + 1;

        const unitTopics = {
            1: 'Introductions & Greetings',
            2: 'Daily Routines',
            3: 'Food & Dining',
            4: 'Travel & Transportation'
        };

        const lessonTypes = {
            1: 'Vocabulary Building',
            2: 'Grammar Focus',
            3: 'Conversation Practice',
            4: 'Listening Comprehension'
        };

        return {
            unit: `Unit ${currentUnit}: ${unitTopics[currentUnit] || 'Advanced Topics'}`,
            lesson: `Lesson ${currentLesson}: ${lessonTypes[currentLesson] || 'Practice Session'}`
        };
    }

    /**
     * Update upcoming class information
     */
    updateUpcomingClass() {
        const classTime = document.querySelector('.class-time .time');
        const classDate = document.querySelector('.class-time .date');
        const teacherName = document.querySelector('.class-details p');
        const joinButton = document.querySelector('.upcoming-class .btn-secondary');

        // Use mock data if no schedule exists
        const upcomingClass = this.currentUser.schedule && this.currentUser.schedule.length > 0
            ? this.currentUser.schedule[0]
            : this.getMockUpcomingClass();

        if (classTime) {
            classTime.textContent = upcomingClass.time;
        }

        if (classDate) {
            classDate.textContent = this.formatDate(upcomingClass.date);
        }

        if (teacherName) {
            teacherName.textContent = `Teacher: ${upcomingClass.teacher}`;
        }

        // Update join button based on class timing
        if (joinButton) {
            const timeUntilClass = this.getTimeUntilClass(upcomingClass);
            if (timeUntilClass <= 15 && timeUntilClass >= 0) {
                joinButton.textContent = 'Join Class Now';
                joinButton.classList.add('btn-primary');
                joinButton.classList.remove('btn-secondary');
            } else if (timeUntilClass < 0) {
                joinButton.textContent = 'Class in Progress';
                joinButton.disabled = true;
            } else {
                joinButton.textContent = `Join in ${timeUntilClass} min`;
            }
        }
    }

    /**
     * Get mock upcoming class data
     */
    getMockUpcomingClass() {
        const now = new Date();
        const classTime = new Date(now);
        classTime.setHours(14, 0, 0, 0); // 2:00 PM today

        return {
            time: '2:00 PM',
            date: new Date().toISOString(),
            teacher: 'Sarah Johnson',
            topic: 'Travel & Tourism',
            dateTime: classTime
        };
    }

    /**
     * Calculate time until class in minutes
     */
    getTimeUntilClass(classInfo) {
        const now = new Date();
        const classTime = classInfo.dateTime || new Date(classInfo.date);
        return Math.floor((classTime - now) / (1000 * 60));
    }

    /**
     * Update study streak display
     */
    updateStudyStreak() {
        const streakNumber = document.querySelector('.streak-number');
        const streakDays = document.querySelectorAll('.streak-calendar .day');
        
        if (streakNumber) {
            streakNumber.textContent = this.currentUser.studyStreak;
        }
        
        // Update streak calendar (mock data)
        streakDays.forEach((day, index) => {
            if (index < this.currentUser.studyStreak) {
                day.classList.add('completed');
            }
        });
    }

    /**
     * Update recent achievements
     */
    updateRecentAchievements() {
        // This would typically fetch real achievement data
        // For now, the achievements are static in the HTML
    }

    /**
     * Handle continue lesson action
     */
    continueLesson() {
        console.log('Continuing lesson...');
        this.showNotification('Loading lesson...', 'info');
        
        // Simulate navigation to lesson
        setTimeout(() => {
            this.showNotification('Lesson loaded successfully!', 'success');
            // In real app: window.location.href = 'lesson.html?unit=3&lesson=2';
        }, 1500);
    }

    /**
     * Handle join class action
     */
    joinClass() {
        const now = new Date();
        const classTime = new Date();
        classTime.setHours(14, 0, 0, 0); // 2:00 PM
        
        const timeDiff = classTime.getTime() - now.getTime();
        const minutesDiff = Math.floor(timeDiff / (1000 * 60));
        
        if (minutesDiff > 15) {
            this.showNotification(`Class starts in ${minutesDiff} minutes. You can join 15 minutes before.`, 'warning');
        } else {
            this.showNotification('Launching Zoom meeting...', 'info');
            setTimeout(() => {
                this.showNotification('Zoom meeting opened in new window!', 'success');
                // In real app: window.open('zoom_meeting_url');
            }, 1000);
        }
    }

    /**
     * Handle AI tutor action
     */
    startAITutor() {
        this.showNotification('Starting AI Tutor...', 'info');
        
        setTimeout(() => {
            this.showNotification('AI Tutor is ready! Start speaking to practice.', 'success');
            // In real app: navigate to AI tutor interface
        }, 1500);
    }

    /**
     * Handle quick action buttons
     */
    handleQuickAction(action) {
        const actions = {
            'Browse Courses': () => {
                this.showNotification('Loading course catalog...', 'info');
                setTimeout(() => {
                    window.location.href = 'courses.html';
                }, 500);
            },
            'Schedule Class': () => this.showNotification('Opening calendar...', 'info'),
            'View Progress': () => this.showNotification('Loading progress report...', 'info'),
            'Daily News': () => this.showNotification('Loading daily news...', 'info')
        };

        if (actions[action]) {
            actions[action]();
        } else {
            this.showNotification(`${action} feature coming soon!`, 'info');
        }
    }

    /**
     * Handle navigation
     */
    handleNavigation(href) {
        const section = href.replace('#', '');
        this.showNotification(`Navigating to ${section}...`, 'info');
        
        // In real app, this would show different sections of the portal
        setTimeout(() => {
            this.showNotification(`${section} section loaded!`, 'success');
        }, 1000);
    }

    /**
     * Show notification to user
     */
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-message">${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `;

        // Add styles if not already added
        this.addNotificationStyles();

        // Add to page
        document.body.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);

        // Add close button functionality
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        });
    }

    /**
     * Add notification styles
     */
    addNotificationStyles() {
        if (document.getElementById('notification-styles')) return;

        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                max-width: 400px;
                padding: 1rem;
                border-radius: 0.5rem;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                animation: slideIn 0.3s ease-out;
            }
            .notification-info {
                background: var(--info-color);
                color: white;
            }
            .notification-success {
                background: var(--success-color);
                color: white;
            }
            .notification-warning {
                background: var(--warning-color);
                color: white;
            }
            .notification-error {
                background: var(--error-color);
                color: white;
            }
            .notification-content {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .notification-close {
                background: none;
                border: none;
                color: inherit;
                font-size: 1.2rem;
                cursor: pointer;
                padding: 0;
                margin-left: 1rem;
            }
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Animate elements on page load
     */
    animateOnLoad() {
        const cards = document.querySelectorAll('.dashboard-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';

            setTimeout(() => {
                card.style.transition = 'all 0.6s ease-out';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100 + (index * 100));
        });

        // Animate progress bars after cards are visible
        setTimeout(() => {
            this.animateProgressBars();
        }, 800);
    }

    /**
     * Animate progress bars with smooth fill effect
     */
    animateProgressBars() {
        // Animate lesson progress bar
        const lessonProgressFill = document.querySelector('.lesson-progress .progress-fill');
        if (lessonProgressFill) {
            const targetWidth = lessonProgressFill.style.width;
            lessonProgressFill.style.width = '0%';
            setTimeout(() => {
                lessonProgressFill.style.transition = 'width 1.5s ease-out';
                lessonProgressFill.style.width = targetWidth;
            }, 100);
        }

        // Animate skill progress bars
        const skillProgressFills = document.querySelectorAll('.skill-progress-fill');
        skillProgressFills.forEach((fill, index) => {
            const targetWidth = fill.style.width;
            fill.style.width = '0%';
            setTimeout(() => {
                fill.style.transition = 'width 1.2s ease-out';
                fill.style.width = targetWidth;
            }, 200 + (index * 150));
        });

        // Animate level circles
        const levelCircles = document.querySelectorAll('.level-circle');
        levelCircles.forEach((circle, index) => {
            circle.style.transform = 'scale(0)';
            setTimeout(() => {
                circle.style.transition = 'transform 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
                circle.style.transform = 'scale(1)';
            }, 300 + (index * 100));
        });
    }

    /**
     * Format date for display
     */
    formatDate(date) {
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        if (date.toDateString() === today.toDateString()) {
            return 'Today, ' + date.toLocaleDateString('en-US', { month: 'long', day: 'numeric' });
        } else if (date.toDateString() === tomorrow.toDateString()) {
            return 'Tomorrow, ' + date.toLocaleDateString('en-US', { month: 'long', day: 'numeric' });
        } else {
            return date.toLocaleDateString('en-US', { 
                weekday: 'long', 
                month: 'long', 
                day: 'numeric' 
            });
        }
    }
}

// Initialize the student application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.studentApp = new StudentApp();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StudentApp;
}
