/**
 * ENGLISH 2025 - Lesson Player Component
 * Handles interactive lesson content delivery and progress tracking
 */

class LessonPlayer {
    constructor() {
        this.authManager = window.authManager;
        this.currentUser = null;
        this.currentStep = 1;
        this.totalSteps = 4;
        this.lessonId = null;
        this.selectedWords = [];
        this.correctAnswer = ['I', 'wake', 'up', 'at', '7', 'AM', 'every', 'morning'];
        this.init();
    }

    /**
     * Initialize the lesson player
     */
    init() {
        this.currentUser = this.authManager.getCurrentUser();
        if (!this.currentUser) {
            window.location.href = '../index.html';
            return;
        }

        // Get lesson ID from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        this.lessonId = urlParams.get('id') || 'B1_Unit3_Lesson2';

        this.setupEventListeners();
        this.updateProgress();
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Audio play buttons
        window.playAudio = (wordId) => this.playAudio(wordId);
        window.playDialogue = () => this.playDialogue();
        window.repeatDialogue = () => this.repeatDialogue();
        window.playLine = (lineNumber) => this.playLine(lineNumber);

        // Exercise functions
        window.selectWord = (element) => this.selectWord(element);
        window.clearSentence = () => this.clearSentence();
        window.checkAnswer = () => this.checkAnswer();

        // Navigation functions
        window.nextStep = () => this.nextStep();
        window.previousStep = () => this.previousStep();

        // Step indicator clicks
        const stepDots = document.querySelectorAll('.step-dot');
        stepDots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                this.goToStep(index + 1);
            });
        });
    }

    /**
     * Play audio for vocabulary words
     */
    playAudio(wordId) {
        console.log(`Playing audio for: ${wordId}`);
        this.showNotification(`Playing pronunciation for "${wordId}"`, 'info');
        
        // In a real app, this would play actual audio files
        // For demo, we'll just show a notification
        setTimeout(() => {
            this.showNotification('Audio playback complete', 'success');
        }, 1000);
    }

    /**
     * Play dialogue audio
     */
    playDialogue() {
        console.log('Playing dialogue');
        this.showNotification('Playing dialogue...', 'info');
        
        // Simulate dialogue playback
        setTimeout(() => {
            this.showNotification('Dialogue playback complete', 'success');
        }, 3000);
    }

    /**
     * Repeat dialogue
     */
    repeatDialogue() {
        this.playDialogue();
    }

    /**
     * Play individual dialogue line
     */
    playLine(lineNumber) {
        console.log(`Playing line ${lineNumber}`);
        this.showNotification(`Playing line ${lineNumber}...`, 'info');
        
        setTimeout(() => {
            this.showNotification('Line playback complete', 'success');
        }, 1500);
    }

    /**
     * Handle word selection in exercise
     */
    selectWord(element) {
        const word = element.textContent;
        
        if (element.disabled) return;
        
        // Add word to sentence
        this.selectedWords.push(word);
        element.disabled = true;
        element.classList.add('selected');
        
        // Update built sentence
        this.updateBuiltSentence();
    }

    /**
     * Update the built sentence display
     */
    updateBuiltSentence() {
        const container = document.getElementById('built-sentence');
        container.innerHTML = '';
        
        this.selectedWords.forEach((word, index) => {
            const wordElement = document.createElement('span');
            wordElement.className = 'built-word';
            wordElement.textContent = word;
            wordElement.onclick = () => this.removeWord(index);
            container.appendChild(wordElement);
        });
    }

    /**
     * Remove word from built sentence
     */
    removeWord(index) {
        const removedWord = this.selectedWords[index];
        this.selectedWords.splice(index, 1);
        
        // Re-enable the word option
        const wordOptions = document.querySelectorAll('.word-option');
        wordOptions.forEach(option => {
            if (option.textContent === removedWord && option.disabled) {
                option.disabled = false;
                option.classList.remove('selected');
                return;
            }
        });
        
        this.updateBuiltSentence();
    }

    /**
     * Clear all selected words
     */
    clearSentence() {
        this.selectedWords = [];
        
        // Re-enable all word options
        const wordOptions = document.querySelectorAll('.word-option');
        wordOptions.forEach(option => {
            option.disabled = false;
            option.classList.remove('selected');
        });
        
        this.updateBuiltSentence();
    }

    /**
     * Check if the built sentence is correct
     */
    checkAnswer() {
        const feedback = document.getElementById('exercise-feedback');
        const isCorrect = this.arraysEqual(this.selectedWords, this.correctAnswer);
        
        if (isCorrect) {
            feedback.textContent = 'Excellent! You got it right!';
            feedback.className = 'exercise-feedback correct';
            
            // Show the target sentence
            document.querySelector('.target-sentence').style.display = 'block';
            
            // Mark exercise as completed
            this.markExerciseCompleted();
            
            this.showNotification('Exercise completed successfully!', 'success');
        } else {
            feedback.textContent = 'Not quite right. Try again!';
            feedback.className = 'exercise-feedback incorrect';
            
            this.showNotification('Try again! Check the word order.', 'error');
        }
    }

    /**
     * Check if two arrays are equal
     */
    arraysEqual(a, b) {
        return a.length === b.length && a.every((val, index) => val === b[index]);
    }

    /**
     * Mark exercise as completed
     */
    markExerciseCompleted() {
        // In a real app, this would update the user's progress
        console.log('Exercise completed for lesson:', this.lessonId);
    }

    /**
     * Navigate to next step
     */
    nextStep() {
        if (this.currentStep < this.totalSteps) {
            this.currentStep++;
            this.showStep(this.currentStep);
            this.updateProgress();
            this.updateNavigation();
        } else {
            // Lesson completed
            this.completeLesson();
        }
    }

    /**
     * Navigate to previous step
     */
    previousStep() {
        if (this.currentStep > 1) {
            this.currentStep--;
            this.showStep(this.currentStep);
            this.updateProgress();
            this.updateNavigation();
        }
    }

    /**
     * Go to specific step
     */
    goToStep(stepNumber) {
        if (stepNumber >= 1 && stepNumber <= this.totalSteps) {
            this.currentStep = stepNumber;
            this.showStep(this.currentStep);
            this.updateProgress();
            this.updateNavigation();
        }
    }

    /**
     * Show specific lesson step
     */
    showStep(stepNumber) {
        // Hide all steps
        const steps = document.querySelectorAll('.lesson-step');
        steps.forEach(step => step.classList.remove('active'));
        
        // Show current step
        const currentStep = document.getElementById(`step-${this.getStepName(stepNumber)}`);
        if (currentStep) {
            currentStep.classList.add('active');
        }
        
        // Update step indicators
        const stepDots = document.querySelectorAll('.step-dot');
        stepDots.forEach((dot, index) => {
            dot.classList.remove('active', 'completed');
            if (index + 1 === stepNumber) {
                dot.classList.add('active');
            } else if (index + 1 < stepNumber) {
                dot.classList.add('completed');
            }
        });
    }

    /**
     * Get step name by number
     */
    getStepName(stepNumber) {
        const stepNames = ['vocabulary', 'dialogue', 'grammar', 'exercise'];
        return stepNames[stepNumber - 1] || 'vocabulary';
    }

    /**
     * Update progress bar
     */
    updateProgress() {
        const progressBar = document.getElementById('lesson-progress-bar');
        const progressText = document.getElementById('progress-text');
        
        const percentage = (this.currentStep / this.totalSteps) * 100;
        
        if (progressBar) {
            progressBar.style.width = `${percentage}%`;
        }
        
        if (progressText) {
            progressText.textContent = `Step ${this.currentStep} of ${this.totalSteps}`;
        }
    }

    /**
     * Update navigation buttons
     */
    updateNavigation() {
        const prevBtn = document.querySelector('.prev-btn');
        const nextBtn = document.querySelector('.next-btn');
        
        if (prevBtn) {
            prevBtn.disabled = this.currentStep === 1;
        }
        
        if (nextBtn) {
            if (this.currentStep === this.totalSteps) {
                nextBtn.textContent = 'Complete Lesson';
            } else {
                nextBtn.textContent = 'Next →';
            }
        }
    }

    /**
     * Complete the lesson
     */
    completeLesson() {
        this.showNotification('Lesson completed! Well done!', 'success');
        
        // In a real app, this would:
        // 1. Mark lesson as completed in user progress
        // 2. Generate homework PDF
        // 3. Update course navigation
        // 4. Redirect to course page or next lesson
        
        setTimeout(() => {
            if (confirm('Lesson completed! Would you like to return to the course page?')) {
                window.location.href = 'courses.html';
            }
        }, 2000);
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        console.log(`[${type.toUpperCase()}] ${message}`);
        
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 24px;
            background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
            color: white;
            border-radius: 8px;
            z-index: 10000;
            animation: slideIn 0.3s ease-out;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// Make LessonPlayer available globally
window.LessonPlayer = LessonPlayer;
