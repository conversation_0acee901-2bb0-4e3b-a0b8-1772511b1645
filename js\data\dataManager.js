/**
 * ENGLISH 2025 - Data Manager
 * Comprehensive data management layer integrating MockDataManager and StorageManager
 */

class DataManager {
    constructor() {
        this.storageManager = window.storageManager || new StorageManager();
        this.mockDataManager = new MockDataManager();
        this.initialized = false;
        this.init();
    }

    /**
     * Initialize the data management system
     */
    async init() {
        try {
            // Initialize mock data if needed
            this.mockDataManager.init();
            
            // Set up data validation schemas
            this.setupValidationSchemas();
            
            // Migrate data if needed
            await this.migrateData();
            
            this.initialized = true;
            console.log('DataManager initialized successfully');
        } catch (error) {
            console.error('Error initializing DataManager:', error);
            throw error;
        }
    }

    /**
     * Set up validation schemas for different data types
     */
    setupValidationSchemas() {
        // Additional validation schemas can be added here
        this.validationSchemas = {
            userSession: {
                required: ['userId', 'role', 'loginTime'],
                fields: {
                    userId: { type: 'string', minLength: 1 },
                    role: { type: 'string', enum: ['student', 'teacher', 'admin'] },
                    loginTime: { type: 'string' }
                }
            },
            userProgress: {
                required: ['userId', 'completedLessons', 'overallProgress'],
                fields: {
                    userId: { type: 'string', minLength: 1 },
                    completedLessons: { type: 'object' },
                    overallProgress: { type: 'number', min: 0, max: 100 }
                }
            }
        };
    }

    /**
     * Migrate data from old format to new format if needed
     */
    async migrateData() {
        const currentVersion = this.storageManager.version;
        const storedVersion = this.storageManager.get('data_version', '1.0');
        
        if (storedVersion !== currentVersion) {
            console.log(`Migrating data from version ${storedVersion} to ${currentVersion}`);
            // Add migration logic here if needed
            this.storageManager.set('data_version', currentVersion);
        }
    }

    /**
     * Get user data by ID and role
     * @param {string} userId - User ID
     * @param {string} role - User role
     * @returns {Object|null} User data
     */
    getUser(userId, role) {
        try {
            const users = this.mockDataManager.getData(role + 's');
            return users ? users.find(user => user.id === userId) : null;
        } catch (error) {
            console.error('Error getting user:', error);
            return null;
        }
    }

    /**
     * Update user data
     * @param {string} userId - User ID
     * @param {string} role - User role
     * @param {Object} updateData - Data to update
     * @returns {boolean} Success status
     */
    updateUser(userId, role, updateData) {
        try {
            const users = this.mockDataManager.getData(role + 's') || [];
            const userIndex = users.findIndex(user => user.id === userId);
            
            if (userIndex === -1) {
                return false;
            }

            // Validate update data
            const validation = this.storageManager.validateWithSchema(updateData, role);
            if (!validation.valid) {
                console.error('Validation errors:', validation.errors);
                return false;
            }

            // Update user data
            users[userIndex] = { ...users[userIndex], ...updateData };
            
            // Save back to storage
            return this.mockDataManager.updateData(role + 's', users);
        } catch (error) {
            console.error('Error updating user:', error);
            return false;
        }
    }

    /**
     * Get student progress data
     * @param {string} studentId - Student ID
     * @returns {Object|null} Progress data
     */
    getStudentProgress(studentId) {
        const student = this.getUser(studentId, 'student');
        return student ? student.progress : null;
    }

    /**
     * Update student progress
     * @param {string} studentId - Student ID
     * @param {Object} progressData - Progress data to update
     * @returns {boolean} Success status
     */
    updateStudentProgress(studentId, progressData) {
        const student = this.getUser(studentId, 'student');
        if (!student) {
            return false;
        }

        const updatedProgress = { ...student.progress, ...progressData };
        return this.updateUser(studentId, 'student', { progress: updatedProgress });
    }

    /**
     * Get teacher schedule
     * @param {string} teacherId - Teacher ID
     * @returns {Object|null} Schedule data
     */
    getTeacherSchedule(teacherId) {
        const teacher = this.getUser(teacherId, 'teacher');
        return teacher ? teacher.schedule : null;
    }

    /**
     * Get courses data with filtering
     * @param {Object} filters - Filter criteria
     * @returns {Array} Filtered courses
     */
    getCourses(filters = {}) {
        try {
            let courses = this.mockDataManager.getData('courses');
            if (!courses) return [];

            // Apply filters
            if (filters.level) {
                courses = courses.levels.filter(level => level.id === filters.level);
            }

            return courses;
        } catch (error) {
            console.error('Error getting courses:', error);
            return [];
        }
    }

    /**
     * Get lessons data with filtering
     * @param {Object} filters - Filter criteria
     * @returns {Array} Filtered lessons
     */
    getLessons(filters = {}) {
        try {
            let lessons = this.mockDataManager.getData('lessons') || [];

            // Apply filters
            if (filters.level) {
                lessons = lessons.filter(lesson => lesson.level === filters.level);
            }
            if (filters.unit) {
                lessons = lessons.filter(lesson => lesson.unit === filters.unit);
            }

            return lessons;
        } catch (error) {
            console.error('Error getting lessons:', error);
            return [];
        }
    }

    /**
     * Get analytics data for admin dashboard
     * @returns {Object} Analytics data
     */
    getAnalytics() {
        try {
            const metrics = this.mockDataManager.getData('metrics');
            const students = this.mockDataManager.getData('students') || [];
            const teachers = this.mockDataManager.getData('teachers') || [];
            const payments = this.mockDataManager.getData('payments') || [];

            return {
                ...metrics,
                realTime: {
                    totalStudents: students.length,
                    totalTeachers: teachers.length,
                    activeStudents: students.filter(s => s.subscriptionStatus === 'active').length,
                    overduePayments: payments.filter(p => p.status === 'overdue').length
                }
            };
        } catch (error) {
            console.error('Error getting analytics:', error);
            return null;
        }
    }

    /**
     * Search across all data types
     * @param {string} query - Search query
     * @param {Array} dataTypes - Data types to search in
     * @returns {Object} Search results
     */
    search(query, dataTypes = ['students', 'teachers', 'lessons', 'courses']) {
        const results = {};

        dataTypes.forEach(dataType => {
            try {
                const data = this.mockDataManager.getData(dataType);
                if (data) {
                    const searchResults = this.storageManager.search(query, 'values');
                    results[dataType] = searchResults.filter(result => 
                        result.key.includes(dataType)
                    );
                }
            } catch (error) {
                console.error(`Error searching in ${dataType}:`, error);
                results[dataType] = [];
            }
        });

        return results;
    }

    /**
     * Export all data for backup
     * @returns {string} JSON backup string
     */
    exportAllData() {
        try {
            const allData = {
                students: this.mockDataManager.getData('students'),
                teachers: this.mockDataManager.getData('teachers'),
                admins: this.mockDataManager.getData('admins'),
                courses: this.mockDataManager.getData('courses'),
                lessons: this.mockDataManager.getData('lessons'),
                exercises: this.mockDataManager.getData('exercises'),
                schedules: this.mockDataManager.getData('schedules'),
                metrics: this.mockDataManager.getData('metrics'),
                notifications: this.mockDataManager.getData('notifications'),
                certificates: this.mockDataManager.getData('certificates'),
                feedback: this.mockDataManager.getData('feedback'),
                payments: this.mockDataManager.getData('payments'),
                supportTickets: this.mockDataManager.getData('supportTickets')
            };

            return JSON.stringify({
                version: this.storageManager.version,
                timestamp: new Date().toISOString(),
                data: allData
            }, null, 2);
        } catch (error) {
            console.error('Error exporting data:', error);
            return null;
        }
    }

    /**
     * Get system health status
     * @returns {Object} System health information
     */
    getSystemHealth() {
        try {
            const storageStats = this.storageManager.getDetailedStats();
            const dataIntegrity = this.checkDataIntegrity();

            return {
                storage: storageStats,
                dataIntegrity,
                initialized: this.initialized,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('Error getting system health:', error);
            return null;
        }
    }

    /**
     * Check data integrity
     * @returns {Object} Data integrity report
     */
    checkDataIntegrity() {
        const report = {
            valid: true,
            errors: [],
            warnings: []
        };

        try {
            // Check if all required data categories exist
            const requiredCategories = ['students', 'teachers', 'admins', 'courses', 'lessons'];
            requiredCategories.forEach(category => {
                const data = this.mockDataManager.getData(category);
                if (!data) {
                    report.errors.push(`Missing data category: ${category}`);
                    report.valid = false;
                }
            });

            // Check data consistency
            const students = this.mockDataManager.getData('students') || [];
            const teachers = this.mockDataManager.getData('teachers') || [];
            
            // Verify student-teacher relationships
            students.forEach(student => {
                if (student.teacherId) {
                    const teacher = teachers.find(t => t.id === student.teacherId);
                    if (!teacher) {
                        report.warnings.push(`Student ${student.id} references non-existent teacher ${student.teacherId}`);
                    }
                }
            });

        } catch (error) {
            report.errors.push('Error during integrity check: ' + error.message);
            report.valid = false;
        }

        return report;
    }
}

// Create global instance
window.dataManager = new DataManager();
