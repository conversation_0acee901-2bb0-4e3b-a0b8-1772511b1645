<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ENGLISH 2025 - ESL Learning Platform</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="main-header">
            <div class="container">
                <div class="header-content">
                    <div class="logo">
                        <h1>ENGLISH 2025</h1>
                        <span class="tagline">Master English with AI-Powered Learning</span>
                    </div>
                    <nav class="main-nav">
                        <a href="#features" class="nav-link">Features</a>
                        <a href="#about" class="nav-link">About</a>
                        <a href="#contact" class="nav-link">Contact</a>
                    </nav>
                </div>
            </div>
        </header>

        <!-- Hero Section -->
        <section class="hero-section">
            <div class="hero-background">
                <img src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80" alt="Students learning" class="hero-image">
                <div class="hero-overlay"></div>
            </div>
            <div class="container">
                <div class="hero-content">
                    <h2 class="hero-title">Choose Your Learning Path</h2>
                    <p class="hero-subtitle">Access your personalized English learning experience</p>
                    
                    <!-- Role Selection Cards -->
                    <div class="role-selection">
                        <div class="role-card" data-role="student">
                            <div class="role-icon">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=387&q=80" alt="Student" class="role-avatar">
                            </div>
                            <h3 class="role-title">Student Portal</h3>
                            <p class="role-description">Access your courses, track progress, schedule classes, and practice with AI tutor</p>
                            <ul class="role-features">
                                <li>Interactive lessons with multimedia content</li>
                                <li>AI-powered pronunciation practice</li>
                                <li>Live classes with certified teachers</li>
                                <li>Progress tracking and certificates</li>
                            </ul>
                            <button class="role-button" data-role="student">Enter Student Portal</button>
                        </div>

                        <div class="role-card" data-role="teacher">
                            <div class="role-icon">
                                <img src="https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=387&q=80" alt="Teacher" class="role-avatar">
                            </div>
                            <h3 class="role-title">Teacher Portal</h3>
                            <p class="role-description">Manage your classes, monitor student progress, and provide personalized feedback</p>
                            <ul class="role-features">
                                <li>Class schedule management</li>
                                <li>Student progress monitoring</li>
                                <li>Feedback and assessment tools</li>
                                <li>Lesson planning resources</li>
                            </ul>
                            <button class="role-button" data-role="teacher">Enter Teacher Portal</button>
                        </div>

                        <div class="role-card" data-role="admin">
                            <div class="role-icon">
                                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=387&q=80" alt="Administrator" class="role-avatar">
                            </div>
                            <h3 class="role-title">Admin Portal</h3>
                            <p class="role-description">Monitor platform health, manage users, and track business metrics</p>
                            <ul class="role-features">
                                <li>System health monitoring</li>
                                <li>User management tools</li>
                                <li>Revenue and analytics dashboard</li>
                                <li>Content management system</li>
                            </ul>
                            <button class="role-button" data-role="admin">Enter Admin Portal</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section id="features" class="features-section">
            <div class="container">
                <h2 class="section-title">Platform Features</h2>
                <div class="features-grid">
                    <div class="feature-card">
                        <img src="https://images.unsplash.com/photo-1434030216411-0b793f4b4173?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" alt="Interactive Learning" class="feature-image">
                        <h3>Interactive Learning</h3>
                        <p>Engage with multimedia content, interactive exercises, and real-time feedback</p>
                    </div>
                    <div class="feature-card">
                        <img src="https://images.unsplash.com/photo-1485827404703-89b55fcc595e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" alt="AI Tutor" class="feature-image">
                        <h3>AI-Powered Tutor</h3>
                        <p>Practice pronunciation and conversation with our advanced AI tutor</p>
                    </div>
                    <div class="feature-card">
                        <img src="https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" alt="Live Classes" class="feature-image">
                        <h3>Live Classes</h3>
                        <p>Join live sessions with certified teachers for personalized instruction</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="main-footer">
            <div class="container">
                <div class="footer-content">
                    <div class="footer-section">
                        <h4>ENGLISH 2025</h4>
                        <p>Empowering English learners worldwide with cutting-edge technology and expert instruction.</p>
                    </div>
                    <div class="footer-section">
                        <h4>Quick Links</h4>
                        <ul>
                            <li><a href="#features">Features</a></li>
                            <li><a href="#about">About</a></li>
                            <li><a href="#contact">Contact</a></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h4>Support</h4>
                        <ul>
                            <li><a href="#help">Help Center</a></li>
                            <li><a href="#privacy">Privacy Policy</a></li>
                            <li><a href="#terms">Terms of Service</a></li>
                        </ul>
                    </div>
                </div>
                <div class="footer-bottom">
                    <p>&copy; 2025 ENGLISH 2025. All rights reserved.</p>
                </div>
            </div>
        </footer>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay hidden">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Loading...</p>
        </div>
    </div>

    <!-- Login Modal -->
    <div id="login-modal" class="modal hidden">
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="login-title">Login to Student Portal</h2>
                <button class="modal-close" onclick="app.closeLoginModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="login-form" onsubmit="app.handleLogin(event)">
                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text" id="username" name="username" required>
                        <small class="form-hint">Demo users: john.doe, sarah.johnson, michael.chen</small>
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" required>
                        <small class="form-hint">Demo password: student123, teacher123, admin123</small>
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary btn-full">Login</button>
                    </div>
                    <div class="form-group">
                        <button type="button" class="btn btn-outline btn-full" onclick="app.demoLogin()">
                            Quick Demo Login
                        </button>
                    </div>
                </form>
                <div id="login-error" class="error-message hidden"></div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/data/storage.js"></script>
    <script src="js/data/mockData.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/app.js"></script>
    <script>
        // Wait for DOM to be ready and initialize app
        document.addEventListener('DOMContentLoaded', function() {
            // Ensure all global objects are available
            if (window.authManager && window.storageManager && window.mockDataManager) {
                window.app = new App();
                console.log('ESL Learning Platform initialized successfully');
            } else {
                console.error('Failed to initialize required managers');
            }
        });
    </script>
</body>
</html>
