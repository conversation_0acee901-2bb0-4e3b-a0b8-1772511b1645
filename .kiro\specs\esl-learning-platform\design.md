# Design Document

## Overview

The ENGLISH 2025 ESL platform prototype will be built as a client-side web application using HTML, JavaScript, and CSS. The design emphasizes a modular, component-based architecture that can demonstrate all core functionality while remaining lightweight and easily deployable. The prototype will simulate backend functionality using local storage and mock data, allowing for a complete user experience demonstration without requiring server infrastructure.

## Architecture

### High-Level Architecture

The application follows a Single Page Application (SPA) pattern with three main user interfaces:

1. **Student Portal** - Dashboard, course content, scheduling, AI tutor
2. **Teacher Portal** - Class management, student progress, feedback tools  
3. **Admin Portal** - System monitoring, user management, analytics

### Technology Stack

- **Frontend Framework**: Vanilla JavaScript with ES6+ modules
- **Styling**: CSS3 with CSS Grid and Flexbox for responsive layouts
- **Data Storage**: LocalStorage for prototype data persistence
- **Audio/Video**: Web Audio API for pronunciation features, HTML5 video for content
- **Voice Recognition**: Web Speech API for AI tutor functionality
- **Charts/Analytics**: Chart.js for progress visualization and admin dashboards
- **Calendar**: Custom JavaScript calendar component
- **PDF Generation**: jsPDF for homework and certificate downloads

### File Structure

```
/esl-platform/
├── index.html                 # Main entry point with role selection
├── css/
│   ├── main.css              # Global styles and variables
│   ├── student.css           # Student portal specific styles
│   ├── teacher.css           # Teacher portal specific styles
│   └── admin.css             # Admin portal specific styles
├── js/
│   ├── app.js                # Main application controller
│   ├── auth.js               # Authentication and role management
│   ├── data/
│   │   ├── mockData.js       # Sample data for prototype
│   │   └── storage.js        # LocalStorage management
│   ├── components/
│   │   ├── dashboard.js      # Reusable dashboard components
│   │   ├── calendar.js       # Scheduling calendar
│   │   ├── coursePlayer.js   # Lesson content player
│   │   ├── aiTutor.js        # AI tutor functionality
│   │   └── progressTracker.js # Progress visualization
│   ├── student/
│   │   ├── studentApp.js     # Student portal controller
│   │   └── lessonPlayer.js   # Interactive lesson components
│   ├── teacher/
│   │   └── teacherApp.js     # Teacher portal controller
│   └── admin/
│       └── adminApp.js       # Admin portal controller
├── assets/
│   ├── images/               # UI images and course content
│   ├── audio/                # Pronunciation audio files
│   └── videos/               # Lesson video content
└── pages/
    ├── student.html          # Student portal
    ├── teacher.html          # Teacher portal
    └── admin.html            # Admin portal
```

## Components and Interfaces

### Core Components

#### 1. Authentication System
- **Purpose**: Role-based access control and user session management
- **Interface**: 
  ```javascript
  class AuthManager {
    login(username, password, role)
    logout()
    getCurrentUser()
    hasPermission(action)
  }
  ```

#### 2. Course Content Player
- **Purpose**: Interactive lesson delivery with multimedia support
- **Interface**:
  ```javascript
  class LessonPlayer {
    loadLesson(levelId, unitId, lessonId)
    playAudio(wordId)
    submitExercise(exerciseData)
    generateHomeworkPDF()
  }
  ```

#### 3. Progress Tracker
- **Purpose**: Visual progress representation and analytics
- **Interface**:
  ```javascript
  class ProgressTracker {
    updateProgress(studentId, activityData)
    getProgressData(studentId)
    generateCertificate(studentId, level)
  }
  ```

#### 4. AI Tutor Engine
- **Purpose**: Voice recognition and conversational AI simulation
- **Interface**:
  ```javascript
  class AITutor {
    startVoiceRecognition()
    scorePronunciation(audioData)
    generateResponse(question)
    displayAvatar(emotion)
  }
  ```

#### 5. Calendar Scheduler
- **Purpose**: Class booking and schedule management
- **Interface**:
  ```javascript
  class CalendarScheduler {
    getAvailableSlots(date, subscriptionType)
    bookClass(studentId, timeSlot)
    rescheduleClass(classId, newTimeSlot)
    generateZoomLink(classId)
  }
  ```

### Data Models

#### Student Model
```javascript
const Student = {
  id: String,
  name: String,
  email: String,
  currentLevel: String, // A1, A2, B1, B2, C1
  unlockedLevels: Array,
  subscriptionType: String, // 8, 12, or 20 classes
  progress: {
    completedLessons: Array,
    unitScores: Object,
    overallProgress: Number
  },
  schedule: Array,
  certificates: Array
}
```

#### Lesson Model
```javascript
const Lesson = {
  id: String,
  level: String,
  unit: Number,
  lessonNumber: Number,
  title: String,
  vocabulary: Array,
  dialogue: Object,
  grammar: Object,
  exercises: Array,
  homeworkPDF: String,
  videos: Array
}
```

#### Teacher Model
```javascript
const Teacher = {
  id: String,
  name: String,
  email: String,
  schedule: Array,
  students: Array,
  classesThisMonth: Number,
  feedback: Array
}
```

## Data Models

### Course Structure
The curriculum follows a hierarchical structure:
- **Levels**: A1 (Beginner) → A2 (Elementary) → B1 (Lower Intermediate) → B2 (Upper Intermediate) → C1 (Advanced)
- **Units**: Each level contains 4 units
- **Lessons**: Each unit contains 4 lessons (16 lessons per level)

### Progress Tracking
Student progress is tracked at multiple granularities:
- Lesson completion status
- Exercise scores within lessons
- Unit exam results
- Overall level progression
- Attendance and participation metrics

## Error Handling

### Client-Side Error Management
1. **Network Simulation**: Mock API responses with realistic delays and occasional failures
2. **Data Validation**: Input validation for all user interactions
3. **Graceful Degradation**: Fallback content when audio/video fails to load
4. **User Feedback**: Clear error messages and loading states

### Error Categories
- **Authentication Errors**: Invalid credentials, session expiry
- **Content Loading Errors**: Missing lessons, broken media files
- **Voice Recognition Errors**: Microphone access denied, speech processing failures
- **Scheduling Conflicts**: Double-booking, invalid time slots
- **Data Persistence Errors**: LocalStorage quota exceeded, data corruption

## Testing Strategy

### Unit Testing
- **Component Testing**: Individual JavaScript modules and functions
- **Data Model Testing**: Validation of data structures and transformations
- **Utility Function Testing**: Helper functions for calculations and formatting

### Integration Testing
- **User Flow Testing**: Complete user journeys through each portal
- **Cross-Component Testing**: Interaction between different system components
- **Data Flow Testing**: Information passing between components

### User Experience Testing
- **Responsive Design Testing**: Multiple screen sizes and devices
- **Accessibility Testing**: Keyboard navigation, screen reader compatibility
- **Performance Testing**: Load times, smooth animations, memory usage

### Browser Compatibility Testing
- **Modern Browsers**: Chrome, Firefox, Safari, Edge (latest versions)
- **Feature Detection**: Graceful fallbacks for unsupported APIs
- **Mobile Testing**: Touch interactions, mobile-specific layouts

## Implementation Phases

### Phase 1: Core Infrastructure
- Basic HTML structure for all three portals
- Authentication system with role switching
- Mock data setup and LocalStorage management
- Responsive CSS framework

### Phase 2: Student Portal
- Dashboard with progress visualization
- Course content player with basic interactivity
- Simple scheduling calendar
- Basic AI tutor simulation

### Phase 3: Teacher Portal
- Class schedule view
- Student progress monitoring
- Feedback submission system

### Phase 4: Admin Portal
- Metrics dashboard with charts
- User management interface
- System monitoring tools

### Phase 5: Advanced Features
- Voice recognition integration
- PDF generation for homework and certificates
- Enhanced AI tutor with avatar animations
- Advanced analytics and reporting

## Security Considerations

### Data Protection
- No sensitive data stored in LocalStorage
- Input sanitization for all user inputs
- XSS prevention through proper DOM manipulation

### Access Control
- Role-based UI rendering
- Function-level permission checks
- Session management with automatic logout

## Performance Optimization

### Loading Strategy
- Lazy loading of course content and media
- Progressive enhancement for advanced features
- Efficient DOM manipulation and event handling

### Caching Strategy
- LocalStorage for user preferences and progress
- Browser caching for static assets
- Efficient data structures for quick lookups

## Accessibility Features

### WCAG Compliance
- Semantic HTML structure
- Proper ARIA labels and roles
- Keyboard navigation support
- High contrast color schemes
- Screen reader compatibility

### Inclusive Design
- Multiple learning modalities (visual, auditory, kinesthetic)
- Adjustable playback speeds for audio content
- Scalable text and UI elements
- Clear visual hierarchy and navigation