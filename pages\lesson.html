<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lesson Player - ENGLISH 2025</title>
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/student.css">
    <link rel="stylesheet" href="../css/lesson.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="lesson-header">
            <div class="container">
                <div class="header-content">
                    <div class="lesson-nav">
                        <button class="back-btn" onclick="goBack()">← Back to Courses</button>
                        <div class="lesson-info">
                            <h1 id="lesson-title">Unit 3: Daily Routines - Lesson 2</h1>
                            <p id="lesson-subtitle">Describing Your Day</p>
                        </div>
                    </div>
                    <div class="lesson-progress">
                        <span id="progress-text">Step 1 of 4</span>
                        <div class="progress-bar">
                            <div class="progress-fill" id="lesson-progress-bar" style="width: 25%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="lesson-content">
            <div class="container">
                <!-- Lesson Steps -->
                <div class="lesson-step active" id="step-vocabulary">
                    <div class="step-header">
                        <h2>📚 Vocabulary</h2>
                        <p>Learn new words and their pronunciation</p>
                    </div>
                    
                    <div class="vocabulary-grid">
                        <div class="vocab-card">
                            <div class="vocab-image">
                                <img src="https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Wake up">
                            </div>
                            <div class="vocab-content">
                                <h3>Wake up</h3>
                                <p class="pronunciation">/weɪk ʌp/</p>
                                <button class="play-audio" onclick="playAudio('wake-up')">🔊</button>
                                <p class="definition">To stop sleeping and become awake</p>
                            </div>
                        </div>
                        
                        <div class="vocab-card">
                            <div class="vocab-image">
                                <img src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Breakfast">
                            </div>
                            <div class="vocab-content">
                                <h3>Breakfast</h3>
                                <p class="pronunciation">/ˈbrekfəst/</p>
                                <button class="play-audio" onclick="playAudio('breakfast')">🔊</button>
                                <p class="definition">The first meal of the day</p>
                            </div>
                        </div>
                        
                        <div class="vocab-card">
                            <div class="vocab-image">
                                <img src="https://images.unsplash.com/photo-1544717297-fa95b6ee9643?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Commute">
                            </div>
                            <div class="vocab-content">
                                <h3>Commute</h3>
                                <p class="pronunciation">/kəˈmjuːt/</p>
                                <button class="play-audio" onclick="playAudio('commute')">🔊</button>
                                <p class="definition">To travel to work regularly</p>
                            </div>
                        </div>
                        
                        <div class="vocab-card">
                            <div class="vocab-image">
                                <img src="https://images.unsplash.com/photo-1541781774459-bb2af2f05b55?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Dinner">
                            </div>
                            <div class="vocab-content">
                                <h3>Dinner</h3>
                                <p class="pronunciation">/ˈdɪnər/</p>
                                <button class="play-audio" onclick="playAudio('dinner')">🔊</button>
                                <p class="definition">The main meal of the day, usually in the evening</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Dialogue Step -->
                <div class="lesson-step" id="step-dialogue">
                    <div class="step-header">
                        <h2>💬 Dialogue</h2>
                        <p>Listen and practice the conversation</p>
                    </div>
                    
                    <div class="dialogue-container">
                        <div class="dialogue-controls">
                            <button class="play-dialogue" onclick="playDialogue()">▶️ Play Dialogue</button>
                            <button class="repeat-dialogue" onclick="repeatDialogue()">🔄 Repeat</button>
                        </div>
                        
                        <div class="dialogue-script">
                            <div class="dialogue-line speaker-a">
                                <div class="speaker">Alex:</div>
                                <div class="text">What time do you usually wake up?</div>
                                <button class="play-line" onclick="playLine(1)">🔊</button>
                            </div>
                            
                            <div class="dialogue-line speaker-b">
                                <div class="speaker">Maria:</div>
                                <div class="text">I wake up at 7 AM every day. What about you?</div>
                                <button class="play-line" onclick="playLine(2)">🔊</button>
                            </div>
                            
                            <div class="dialogue-line speaker-a">
                                <div class="speaker">Alex:</div>
                                <div class="text">I'm an early bird! I wake up at 6 AM and have breakfast at 6:30.</div>
                                <button class="play-line" onclick="playLine(3)">🔊</button>
                            </div>
                            
                            <div class="dialogue-line speaker-b">
                                <div class="speaker">Maria:</div>
                                <div class="text">That's early! I usually have breakfast after I shower and get dressed.</div>
                                <button class="play-line" onclick="playLine(4)">🔊</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Grammar Step -->
                <div class="lesson-step" id="step-grammar">
                    <div class="step-header">
                        <h2>📖 Grammar Focus</h2>
                        <p>Learn about present simple for daily routines</p>
                    </div>
                    
                    <div class="grammar-content">
                        <div class="grammar-rule">
                            <h3>Present Simple for Daily Routines</h3>
                            <p>We use the present simple to talk about things we do regularly or habitually.</p>
                            
                            <div class="grammar-examples">
                                <h4>Examples:</h4>
                                <ul>
                                    <li>I <strong>wake up</strong> at 7 AM.</li>
                                    <li>She <strong>has</strong> breakfast at 8 AM.</li>
                                    <li>We <strong>go</strong> to work by bus.</li>
                                    <li>They <strong>watch</strong> TV in the evening.</li>
                                </ul>
                            </div>
                            
                            <div class="grammar-structure">
                                <h4>Structure:</h4>
                                <div class="structure-box">
                                    <p><strong>Subject + Verb (base form) + Object/Time</strong></p>
                                    <p><em>Note: Add 's' or 'es' for he/she/it</em></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Exercise Step -->
                <div class="lesson-step" id="step-exercise">
                    <div class="step-header">
                        <h2>✏️ Practice Exercise</h2>
                        <p>Put the words in the correct order</p>
                    </div>
                    
                    <div class="exercise-container">
                        <div class="exercise-question">
                            <h3>Unscramble the sentence:</h3>
                            <p class="target-sentence">I wake up at 7 AM every morning.</p>
                        </div>
                        
                        <div class="word-bank">
                            <div class="word-options">
                                <button class="word-option" onclick="selectWord(this)">morning</button>
                                <button class="word-option" onclick="selectWord(this)">wake</button>
                                <button class="word-option" onclick="selectWord(this)">I</button>
                                <button class="word-option" onclick="selectWord(this)">every</button>
                                <button class="word-option" onclick="selectWord(this)">up</button>
                                <button class="word-option" onclick="selectWord(this)">at</button>
                                <button class="word-option" onclick="selectWord(this)">AM</button>
                                <button class="word-option" onclick="selectWord(this)">7</button>
                            </div>
                        </div>
                        
                        <div class="sentence-builder">
                            <div class="built-sentence" id="built-sentence">
                                <!-- Selected words will appear here -->
                            </div>
                            <button class="clear-sentence" onclick="clearSentence()">Clear</button>
                        </div>
                        
                        <div class="exercise-actions">
                            <button class="check-answer" onclick="checkAnswer()">Check Answer</button>
                            <div class="exercise-feedback" id="exercise-feedback"></div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Navigation Footer -->
        <footer class="lesson-footer">
            <div class="container">
                <div class="lesson-navigation">
                    <button class="nav-btn prev-btn" onclick="previousStep()" disabled>← Previous</button>
                    <div class="step-indicators">
                        <span class="step-dot active" data-step="1"></span>
                        <span class="step-dot" data-step="2"></span>
                        <span class="step-dot" data-step="3"></span>
                        <span class="step-dot" data-step="4"></span>
                    </div>
                    <button class="nav-btn next-btn" onclick="nextStep()">Next →</button>
                </div>
            </div>
        </footer>
    </div>

    <!-- Scripts -->
    <script src="../js/data/storage.js"></script>
    <script src="../js/data/mockData.js"></script>
    <script src="../js/auth.js"></script>
    <script src="../js/components/lessonPlayer.js"></script>
    <script>
        function goBack() {
            window.location.href = 'courses.html';
        }

        // Initialize lesson player
        document.addEventListener('DOMContentLoaded', function() {
            if (window.authManager && window.authManager.isAuthenticated()) {
                window.lessonPlayer = new LessonPlayer();
            } else {
                window.location.href = '../index.html';
            }
        });
    </script>
</body>
</html>
