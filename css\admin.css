/* Admin Portal Specific Styles */

/* Portal Header */
.portal-header {
  background: linear-gradient(135deg, var(--gray-800), var(--gray-900));
  color: var(--white);
  box-shadow: var(--shadow-lg);
}

.portal-header .logo h1 {
  color: var(--white);
  margin-bottom: var(--spacing-1);
}

.portal-type {
  font-size: var(--font-size-sm);
  opacity: 0.9;
}

.portal-nav {
  display: flex;
  gap: var(--spacing-6);
}

.portal-nav .nav-link {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.portal-nav .nav-link:hover,
.portal-nav .nav-link.active {
  color: var(--white);
  background-color: rgba(255, 255, 255, 0.1);
}

.user-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.user-avatar img {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.user-name {
  font-weight: 500;
  color: var(--white);
}

.logout-btn {
  background: rgba(255, 255, 255, 0.1);
  color: var(--white);
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Main Content */
.main-content {
  padding: var(--spacing-8) 0;
  min-height: calc(100vh - 80px);
}

/* Welcome Section */
.welcome-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-8);
  padding: var(--spacing-6);
  background: linear-gradient(135deg, var(--white), var(--gray-50));
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
}

.welcome-content h2 {
  font-size: var(--font-size-3xl);
  color: var(--gray-900);
  margin-bottom: var(--spacing-2);
}

.welcome-content p {
  color: var(--gray-600);
  font-size: var(--font-size-lg);
  margin-bottom: 0;
}

.system-status {
  display: flex;
  align-items: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-4);
  border-radius: var(--radius-lg);
  font-weight: 500;
}

.status-indicator.online {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  background: var(--success-color);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Metrics Section */
.metrics-section {
  margin-bottom: var(--spacing-8);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-6);
}

.metric-card {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  transition: all var(--transition-normal);
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.metric-icon {
  font-size: var(--font-size-3xl);
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gray-100);
  border-radius: var(--radius-xl);
}

.metric-content {
  flex: 1;
}

.metric-number {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
}

.metric-label {
  color: var(--gray-600);
  margin-bottom: var(--spacing-1);
}

.metric-change {
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.metric-change.positive {
  color: var(--success-color);
}

.metric-change.negative {
  color: var(--error-color);
}

/* Dashboard Grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-6);
}

.dashboard-card {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.dashboard-card h3 {
  font-size: var(--font-size-xl);
  color: var(--gray-900);
  margin-bottom: var(--spacing-4);
  border-bottom: 2px solid var(--gray-100);
  padding-bottom: var(--spacing-2);
}

/* User Management Card */
.user-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: var(--spacing-6);
  padding: var(--spacing-4);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
}

.user-type {
  text-align: center;
}

.user-count {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--spacing-1);
}

.user-label {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.recent-users h4 {
  font-size: var(--font-size-lg);
  color: var(--gray-900);
  margin-bottom: var(--spacing-3);
}

.user-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-4);
}

.user-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
}

.user-item img {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
}

.user-info {
  flex: 1;
}

.user-name {
  font-weight: 500;
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
}

.user-role {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.user-date {
  font-size: var(--font-size-sm);
  color: var(--gray-500);
}

/* Revenue Card */
.revenue-chart {
  text-align: center;
  padding: var(--spacing-8);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-6);
  color: var(--gray-500);
  font-size: var(--font-size-lg);
}

.revenue-breakdown {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.revenue-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-3);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
}

.revenue-source {
  color: var(--gray-700);
}

.revenue-amount {
  font-weight: 600;
  color: var(--success-color);
}

/* Teacher Performance Card */
.teacher-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-4);
}

.teacher-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
}

.teacher-item img {
  width: 50px;
  height: 50px;
  border-radius: var(--radius-full);
}

.teacher-info {
  flex: 1;
}

.teacher-name {
  font-weight: 500;
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
}

.teacher-stats {
  display: flex;
  gap: var(--spacing-3);
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.teacher-workload {
  text-align: right;
  min-width: 100px;
}

.workload-bar {
  width: 100%;
  height: 6px;
  background: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-bottom: var(--spacing-1);
}

.workload-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--success-color), var(--warning-color));
  border-radius: var(--radius-full);
}

.teacher-workload span {
  font-size: var(--font-size-xs);
  color: var(--gray-600);
}

/* System Health Card */
.health-metrics {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.health-item {
  text-align: center;
  padding: var(--spacing-3);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
}

.health-label {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  margin-bottom: var(--spacing-1);
}

.health-value {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-900);
}

.health-value.good {
  color: var(--success-color);
}

.alerts-section h4 {
  font-size: var(--font-size-lg);
  color: var(--gray-900);
  margin-bottom: var(--spacing-3);
}

.alert-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-2);
}

.alert-item.resolved {
  opacity: 0.7;
}

.alert-icon {
  font-size: var(--font-size-lg);
}

.alert-text {
  flex: 1;
  color: var(--gray-700);
}

.alert-time {
  font-size: var(--font-size-xs);
  color: var(--gray-500);
}

/* Support Tickets Card */
.ticket-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: var(--spacing-6);
  padding: var(--spacing-4);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
}

.ticket-stat {
  text-align: center;
}

.ticket-number {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--warning-color);
  margin-bottom: var(--spacing-1);
}

.ticket-label {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.recent-tickets h4 {
  font-size: var(--font-size-lg);
  color: var(--gray-900);
  margin-bottom: var(--spacing-3);
}

.ticket-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-4);
}

.ticket-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
}

.ticket-priority {
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
}

.ticket-priority.high {
  background: var(--error-color);
  color: var(--white);
}

.ticket-priority.medium {
  background: var(--warning-color);
  color: var(--white);
}

.ticket-info {
  flex: 1;
}

.ticket-title {
  font-weight: 500;
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
}

.ticket-user {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.ticket-time {
  font-size: var(--font-size-xs);
  color: var(--gray-500);
}

/* Quick Actions Card */
.admin-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-3);
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-4);
  background: var(--gray-50);
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  color: var(--gray-700);
}

.action-btn:hover {
  background: var(--gray-800);
  color: var(--white);
  transform: translateY(-2px);
}

.action-icon {
  font-size: var(--font-size-xl);
}

.action-btn span:last-child {
  font-size: var(--font-size-sm);
  font-weight: 500;
  text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .welcome-section {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-4);
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .portal-nav {
    display: none;
  }
  
  .user-menu {
    gap: var(--spacing-2);
  }
  
  .user-name {
    display: none;
  }
  
  .user-stats {
    flex-direction: column;
    gap: var(--spacing-3);
  }
  
  .health-metrics {
    grid-template-columns: 1fr;
  }
  
  .ticket-stats {
    flex-direction: column;
    gap: var(--spacing-3);
  }
  
  .admin-actions {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .metric-card {
    flex-direction: column;
    text-align: center;
  }
  
  .teacher-item,
  .user-item,
  .ticket-item {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-2);
  }
}
