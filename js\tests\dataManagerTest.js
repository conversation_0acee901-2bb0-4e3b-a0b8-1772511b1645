/**
 * ENGLISH 2025 - Data Manager Tests
 * Test suite for data management functionality
 */

class DataManagerTest {
    constructor() {
        this.testResults = [];
        this.passedTests = 0;
        this.failedTests = 0;
    }

    /**
     * Run all tests
     */
    async runAllTests() {
        console.log('🧪 Starting Data Manager Tests...\n');

        // Test basic initialization
        await this.testInitialization();
        
        // Test storage operations
        await this.testStorageOperations();
        
        // Test data validation
        await this.testDataValidation();
        
        // Test user operations
        await this.testUserOperations();
        
        // Test data integrity
        await this.testDataIntegrity();
        
        // Test search functionality
        await this.testSearchFunctionality();

        // Print results
        this.printResults();
    }

    /**
     * Test initialization
     */
    async testInitialization() {
        try {
            this.assert(window.dataManager !== undefined, 'DataManager should be initialized');
            this.assert(window.dataManager.initialized === true, 'DataManager should be marked as initialized');
            this.assert(window.storageManager !== undefined, 'StorageManager should be available');
            this.pass('Initialization test');
        } catch (error) {
            this.fail('Initialization test', error.message);
        }
    }

    /**
     * Test storage operations
     */
    async testStorageOperations() {
        try {
            const testData = { test: 'value', number: 42 };
            
            // Test basic set/get
            const setResult = window.storageManager.set('test_key', testData);
            this.assert(setResult === true, 'Storage set should return true');
            
            const getData = window.storageManager.get('test_key');
            this.assert(JSON.stringify(getData) === JSON.stringify(testData), 'Retrieved data should match stored data');
            
            // Test with validation
            const validationResult = window.storageManager.setWithValidation('test_student', {
                id: 'test_001',
                username: 'test.user',
                email: '<EMAIL>',
                name: 'Test User',
                role: 'student',
                currentLevel: 'A1'
            }, 'student');
            
            this.assert(validationResult.success === true, 'Validation should pass for valid student data');
            
            // Clean up
            window.storageManager.remove('test_key');
            window.storageManager.remove('test_student');
            
            this.pass('Storage operations test');
        } catch (error) {
            this.fail('Storage operations test', error.message);
        }
    }

    /**
     * Test data validation
     */
    async testDataValidation() {
        try {
            // Test valid student data
            const validStudent = {
                id: 'student_test',
                username: 'valid.user',
                email: '<EMAIL>',
                name: 'Valid User',
                role: 'student',
                currentLevel: 'B1'
            };
            
            const validResult = window.storageManager.validateWithSchema(validStudent, 'student');
            this.assert(validResult.valid === true, 'Valid student data should pass validation');
            
            // Test invalid student data
            const invalidStudent = {
                id: 'student_test',
                username: 'ab', // Too short
                email: 'invalid-email', // Invalid format
                name: 'Invalid User',
                role: 'student',
                currentLevel: 'X1' // Invalid level
            };
            
            const invalidResult = window.storageManager.validateWithSchema(invalidStudent, 'student');
            this.assert(invalidResult.valid === false, 'Invalid student data should fail validation');
            this.assert(invalidResult.errors.length > 0, 'Validation should return errors for invalid data');
            
            this.pass('Data validation test');
        } catch (error) {
            this.fail('Data validation test', error.message);
        }
    }

    /**
     * Test user operations
     */
    async testUserOperations() {
        try {
            // Test getting user data
            const student = window.dataManager.getUser('student_001', 'student');
            this.assert(student !== null, 'Should be able to retrieve existing student');
            this.assert(student.id === 'student_001', 'Retrieved student should have correct ID');
            
            const teacher = window.dataManager.getUser('teacher_001', 'teacher');
            this.assert(teacher !== null, 'Should be able to retrieve existing teacher');
            this.assert(teacher.role === 'teacher', 'Retrieved teacher should have correct role');
            
            // Test getting non-existent user
            const nonExistent = window.dataManager.getUser('nonexistent', 'student');
            this.assert(nonExistent === null, 'Should return null for non-existent user');
            
            // Test getting student progress
            const progress = window.dataManager.getStudentProgress('student_001');
            this.assert(progress !== null, 'Should be able to retrieve student progress');
            this.assert(typeof progress.overallProgress === 'number', 'Progress should contain numeric overall progress');
            
            this.pass('User operations test');
        } catch (error) {
            this.fail('User operations test', error.message);
        }
    }

    /**
     * Test data integrity
     */
    async testDataIntegrity() {
        try {
            const healthStatus = window.dataManager.getSystemHealth();
            this.assert(healthStatus !== null, 'Should be able to get system health status');
            this.assert(healthStatus.initialized === true, 'System should be initialized');
            
            const integrityReport = healthStatus.dataIntegrity;
            this.assert(integrityReport !== null, 'Should have data integrity report');
            this.assert(typeof integrityReport.valid === 'boolean', 'Integrity report should have valid flag');
            
            // Test analytics data
            const analytics = window.dataManager.getAnalytics();
            this.assert(analytics !== null, 'Should be able to get analytics data');
            this.assert(analytics.platform !== undefined, 'Analytics should contain platform metrics');
            
            this.pass('Data integrity test');
        } catch (error) {
            this.fail('Data integrity test', error.message);
        }
    }

    /**
     * Test search functionality
     */
    async testSearchFunctionality() {
        try {
            // Test searching for students
            const searchResults = window.dataManager.search('john', ['students']);
            this.assert(searchResults !== null, 'Search should return results object');
            this.assert(searchResults.students !== undefined, 'Search results should contain students array');
            
            // Test storage manager search
            const storageSearch = window.storageManager.search('student*', 'keys');
            this.assert(Array.isArray(storageSearch), 'Storage search should return array');
            
            this.pass('Search functionality test');
        } catch (error) {
            this.fail('Search functionality test', error.message);
        }
    }

    /**
     * Assert a condition
     */
    assert(condition, message) {
        if (!condition) {
            throw new Error(`Assertion failed: ${message}`);
        }
    }

    /**
     * Mark test as passed
     */
    pass(testName) {
        this.testResults.push({ name: testName, status: 'PASS' });
        this.passedTests++;
        console.log(`✅ ${testName} - PASSED`);
    }

    /**
     * Mark test as failed
     */
    fail(testName, error) {
        this.testResults.push({ name: testName, status: 'FAIL', error });
        this.failedTests++;
        console.log(`❌ ${testName} - FAILED: ${error}`);
    }

    /**
     * Print test results summary
     */
    printResults() {
        console.log('\n📊 Test Results Summary:');
        console.log(`Total Tests: ${this.testResults.length}`);
        console.log(`Passed: ${this.passedTests}`);
        console.log(`Failed: ${this.failedTests}`);
        console.log(`Success Rate: ${Math.round((this.passedTests / this.testResults.length) * 100)}%`);
        
        if (this.failedTests === 0) {
            console.log('\n🎉 All tests passed! Data management system is working correctly.');
        } else {
            console.log('\n⚠️ Some tests failed. Please check the implementation.');
        }
    }
}

// Auto-run tests when loaded
document.addEventListener('DOMContentLoaded', async () => {
    // Wait a bit for other scripts to load
    setTimeout(async () => {
        const tester = new DataManagerTest();
        await tester.runAllTests();
    }, 1000);
});

// Export for manual testing
window.DataManagerTest = DataManagerTest;
