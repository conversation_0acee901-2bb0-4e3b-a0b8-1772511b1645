/**
 * ENGLISH 2025 - Authentication Manager
 * Handles user authentication, role management, and session persistence
 */

class AuthManager {
    constructor() {
        this.currentUser = null;
        this.sessionKey = 'esl_user_session';
        this.usersKey = 'esl_users_data';
        this.init();
    }

    /**
     * Initialize the authentication manager
     */
    init() {
        this.loadSession();
        this.initializeMockUsers();
    }

    /**
     * Initialize mock users for prototype
     */
    initializeMockUsers() {
        const existingUsers = localStorage.getItem(this.usersKey);
        if (!existingUsers) {
            const mockUsers = {
                students: [
                    {
                        id: 'student_001',
                        username: 'john.doe',
                        password: 'student123',
                        email: '<EMAIL>',
                        name: '<PERSON>',
                        role: 'student',
                        currentLevel: 'B1',
                        unlockedLevels: ['A1', 'A2', 'B1'],
                        subscriptionType: '20',
                        progress: {
                            completedLessons: 12,
                            unitScores: {
                                'A1_Unit1': 85,
                                'A1_Unit2': 92,
                                'A2_Unit1': 78,
                                'A2_Unit2': 88,
                                'B1_Unit1': 82,
                                'B1_Unit2': 75
                            },
                            overallProgress: 75
                        },
                        certificates: ['A1_Certificate', 'A2_Certificate'],
                        studyStreak: 7,
                        lastLogin: null,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 'student_002',
                        username: 'maria.garcia',
                        password: 'student123',
                        email: '<EMAIL>',
                        name: 'Maria Garcia',
                        role: 'student',
                        currentLevel: 'A2',
                        unlockedLevels: ['A1', 'A2'],
                        subscriptionType: '10',
                        progress: {
                            completedLessons: 8,
                            unitScores: {
                                'A1_Unit1': 90,
                                'A1_Unit2': 88,
                                'A2_Unit1': 85
                            },
                            overallProgress: 60
                        },
                        certificates: ['A1_Certificate'],
                        studyStreak: 3,
                        lastLogin: null,
                        createdAt: new Date().toISOString()
                    }
                ],
                teachers: [
                    {
                        id: 'teacher_001',
                        username: 'sarah.johnson',
                        password: 'teacher123',
                        email: '<EMAIL>',
                        name: 'Sarah Johnson',
                        role: 'teacher',
                        specialization: 'Conversation & Pronunciation',
                        experience: '5 years',
                        rating: 4.9,
                        students: ['student_001', 'student_002'],
                        schedule: {
                            monday: ['09:00', '10:00', '14:00', '15:00'],
                            tuesday: ['09:00', '10:00', '11:00', '14:00'],
                            wednesday: ['09:00', '14:00', '15:00', '16:00'],
                            thursday: ['10:00', '11:00', '14:00', '15:00'],
                            friday: ['09:00', '10:00', '14:00', '15:00']
                        },
                        permissions: ['view_students', 'manage_classes', 'grade_assignments'],
                        lastLogin: null,
                        createdAt: new Date().toISOString()
                    }
                ],
                admins: [
                    {
                        id: 'admin_001',
                        username: 'michael.chen',
                        password: 'admin123',
                        email: '<EMAIL>',
                        name: 'Michael Chen',
                        role: 'admin',
                        title: 'System Administrator',
                        permissions: [
                            'user_management',
                            'system_monitoring',
                            'content_management',
                            'analytics',
                            'financial_reports',
                            'teacher_management',
                            'student_management'
                        ],
                        lastLogin: null,
                        createdAt: new Date().toISOString()
                    }
                ]
            };
            localStorage.setItem(this.usersKey, JSON.stringify(mockUsers));
        }
    }

    /**
     * Authenticate user with username/password
     * @param {string} username - User's username
     * @param {string} password - User's password
     * @param {string} role - Expected role (optional, for validation)
     * @returns {Object} Authentication result
     */
    async login(username, password, role = null) {
        try {
            const users = JSON.parse(localStorage.getItem(this.usersKey));
            let user = null;

            // Search for user in all role categories
            for (const roleCategory of ['students', 'teachers', 'admins']) {
                const foundUser = users[roleCategory].find(u => 
                    u.username === username && u.password === password
                );
                if (foundUser) {
                    user = foundUser;
                    break;
                }
            }

            if (!user) {
                return {
                    success: false,
                    error: 'Invalid username or password',
                    code: 'INVALID_CREDENTIALS'
                };
            }

            // Validate role if specified
            if (role && user.role !== role) {
                return {
                    success: false,
                    error: `Access denied. Expected ${role} role.`,
                    code: 'ROLE_MISMATCH'
                };
            }

            // Update last login
            user.lastLogin = new Date().toISOString();
            this.updateUserData(user);

            // Create session
            this.currentUser = user;
            this.saveSession();

            return {
                success: true,
                user: this.sanitizeUser(user),
                message: 'Login successful'
            };

        } catch (error) {
            console.error('Login error:', error);
            return {
                success: false,
                error: 'Authentication system error',
                code: 'SYSTEM_ERROR'
            };
        }
    }

    /**
     * Log out current user
     */
    logout() {
        this.currentUser = null;
        localStorage.removeItem(this.sessionKey);
        sessionStorage.clear();
        
        // Dispatch logout event for other components
        window.dispatchEvent(new CustomEvent('userLogout'));
        
        return {
            success: true,
            message: 'Logged out successfully'
        };
    }

    /**
     * Get current authenticated user
     * @returns {Object|null} Current user or null if not authenticated
     */
    getCurrentUser() {
        return this.currentUser ? this.sanitizeUser(this.currentUser) : null;
    }

    /**
     * Check if user is authenticated
     * @returns {boolean} True if user is authenticated
     */
    isAuthenticated() {
        return this.currentUser !== null;
    }

    /**
     * Check if current user has specific permission
     * @param {string} permission - Permission to check
     * @returns {boolean} True if user has permission
     */
    hasPermission(permission) {
        if (!this.currentUser || !this.currentUser.permissions) {
            return false;
        }
        return this.currentUser.permissions.includes(permission);
    }

    /**
     * Check if current user has specific role
     * @param {string} role - Role to check
     * @returns {boolean} True if user has role
     */
    hasRole(role) {
        return this.currentUser && this.currentUser.role === role;
    }

    /**
     * Switch user role (for demo purposes)
     * @param {string} newRole - New role to switch to
     * @returns {Object} Switch result
     */
    switchRole(newRole) {
        if (!this.currentUser) {
            return {
                success: false,
                error: 'No user authenticated',
                code: 'NOT_AUTHENTICATED'
            };
        }

        // For demo purposes, allow role switching
        // In production, this would require proper authorization
        const validRoles = ['student', 'teacher', 'admin'];
        if (!validRoles.includes(newRole)) {
            return {
                success: false,
                error: 'Invalid role specified',
                code: 'INVALID_ROLE'
            };
        }

        this.currentUser.role = newRole;
        this.saveSession();

        return {
            success: true,
            message: `Role switched to ${newRole}`,
            user: this.sanitizeUser(this.currentUser)
        };
    }

    /**
     * Save current session to localStorage
     */
    saveSession() {
        if (this.currentUser) {
            const sessionData = {
                user: this.currentUser,
                timestamp: new Date().toISOString(),
                expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours
            };
            localStorage.setItem(this.sessionKey, JSON.stringify(sessionData));
        }
    }

    /**
     * Load session from localStorage
     */
    loadSession() {
        try {
            const sessionData = localStorage.getItem(this.sessionKey);
            if (sessionData) {
                const session = JSON.parse(sessionData);
                const now = new Date();
                const expiresAt = new Date(session.expiresAt);

                if (now < expiresAt) {
                    this.currentUser = session.user;
                    return true;
                } else {
                    // Session expired
                    localStorage.removeItem(this.sessionKey);
                }
            }
        } catch (error) {
            console.error('Error loading session:', error);
            localStorage.removeItem(this.sessionKey);
        }
        return false;
    }

    /**
     * Update user data in storage
     * @param {Object} userData - Updated user data
     */
    updateUserData(userData) {
        try {
            const users = JSON.parse(localStorage.getItem(this.usersKey));
            const roleCategory = userData.role + 's';
            
            if (users[roleCategory]) {
                const userIndex = users[roleCategory].findIndex(u => u.id === userData.id);
                if (userIndex !== -1) {
                    users[roleCategory][userIndex] = userData;
                    localStorage.setItem(this.usersKey, JSON.stringify(users));
                }
            }
        } catch (error) {
            console.error('Error updating user data:', error);
        }
    }

    /**
     * Remove sensitive data from user object
     * @param {Object} user - User object
     * @returns {Object} Sanitized user object
     */
    sanitizeUser(user) {
        const { password, ...sanitizedUser } = user;
        return sanitizedUser;
    }

    /**
     * Sanitize data for storage
     * @param {*} data - Data to sanitize
     * @returns {*} Sanitized data
     */
    sanitizeData(data) {
        if (typeof data === 'string') {
            // Remove potentially harmful scripts and HTML
            return data.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                      .replace(/<[^>]*>/g, '')
                      .trim();
        }

        if (typeof data === 'object' && data !== null) {
            if (Array.isArray(data)) {
                return data.map(item => this.sanitizeData(item));
            } else {
                const sanitized = {};
                for (const key in data) {
                    if (data.hasOwnProperty(key)) {
                        sanitized[key] = this.sanitizeData(data[key]);
                    }
                }
                return sanitized;
            }
        }

        return data;
    }

    /**
     * Get all users (admin only)
     * @returns {Object|null} All users or null if not authorized
     */
    getAllUsers() {
        if (!this.hasRole('admin')) {
            return null;
        }

        try {
            const users = JSON.parse(localStorage.getItem(this.usersKey));
            // Sanitize all users
            const sanitizedUsers = {};
            for (const role in users) {
                sanitizedUsers[role] = users[role].map(user => this.sanitizeUser(user));
            }
            return sanitizedUsers;
        } catch (error) {
            console.error('Error getting all users:', error);
            return null;
        }
    }

    /**
     * Validate session and refresh if needed
     * @returns {boolean} True if session is valid
     */
    validateSession() {
        return this.loadSession();
    }

    /**
     * Get user permissions based on role
     * @param {string} role - User role
     * @returns {Array} Array of permissions
     */
    getRolePermissions(role) {
        const rolePermissions = {
            student: [
                'view_own_profile',
                'view_courses',
                'take_lessons',
                'schedule_classes',
                'view_progress',
                'download_certificates',
                'submit_homework',
                'use_ai_tutor'
            ],
            teacher: [
                'view_own_profile',
                'view_students',
                'manage_classes',
                'grade_assignments',
                'view_student_progress',
                'create_feedback',
                'manage_schedule',
                'access_teaching_resources'
            ],
            admin: [
                'view_all_profiles',
                'user_management',
                'system_monitoring',
                'content_management',
                'analytics',
                'financial_reports',
                'teacher_management',
                'student_management',
                'platform_settings',
                'backup_restore'
            ]
        };

        return rolePermissions[role] || [];
    }

    /**
     * Check multiple permissions at once
     * @param {Array} permissions - Array of permissions to check
     * @returns {Object} Object with permission results
     */
    checkPermissions(permissions) {
        const results = {};
        permissions.forEach(permission => {
            results[permission] = this.hasPermission(permission);
        });
        return results;
    }

    /**
     * Get navigation items based on user role and permissions
     * @returns {Array} Array of navigation items
     */
    getNavigationItems() {
        if (!this.currentUser) return [];

        const navigationMap = {
            student: [
                { id: 'dashboard', label: 'Dashboard', icon: 'dashboard', permission: 'view_own_profile' },
                { id: 'courses', label: 'My Courses', icon: 'book', permission: 'view_courses' },
                { id: 'schedule', label: 'Schedule', icon: 'calendar', permission: 'schedule_classes' },
                { id: 'progress', label: 'Progress', icon: 'chart', permission: 'view_progress' },
                { id: 'ai-tutor', label: 'AI Tutor', icon: 'robot', permission: 'use_ai_tutor' },
                { id: 'certificates', label: 'Certificates', icon: 'award', permission: 'download_certificates' }
            ],
            teacher: [
                { id: 'dashboard', label: 'Dashboard', icon: 'dashboard', permission: 'view_own_profile' },
                { id: 'students', label: 'My Students', icon: 'users', permission: 'view_students' },
                { id: 'classes', label: 'Classes', icon: 'calendar', permission: 'manage_classes' },
                { id: 'assignments', label: 'Assignments', icon: 'clipboard', permission: 'grade_assignments' },
                { id: 'resources', label: 'Resources', icon: 'folder', permission: 'access_teaching_resources' },
                { id: 'schedule', label: 'Schedule', icon: 'clock', permission: 'manage_schedule' }
            ],
            admin: [
                { id: 'dashboard', label: 'Dashboard', icon: 'dashboard', permission: 'system_monitoring' },
                { id: 'users', label: 'User Management', icon: 'users', permission: 'user_management' },
                { id: 'analytics', label: 'Analytics', icon: 'chart', permission: 'analytics' },
                { id: 'content', label: 'Content', icon: 'book', permission: 'content_management' },
                { id: 'teachers', label: 'Teachers', icon: 'user-tie', permission: 'teacher_management' },
                { id: 'students', label: 'Students', icon: 'graduation-cap', permission: 'student_management' },
                { id: 'finance', label: 'Finance', icon: 'dollar', permission: 'financial_reports' },
                { id: 'settings', label: 'Settings', icon: 'cog', permission: 'platform_settings' }
            ]
        };

        const items = navigationMap[this.currentUser.role] || [];
        return items.filter(item => this.hasPermission(item.permission));
    }

    /**
     * Create demo login for testing
     * @param {string} role - Role to login as
     * @returns {Object} Login result
     */
    demoLogin(role) {
        const demoCredentials = {
            student: { username: 'john.doe', password: 'student123' },
            teacher: { username: 'sarah.johnson', password: 'teacher123' },
            admin: { username: 'michael.chen', password: 'admin123' }
        };

        const credentials = demoCredentials[role];
        if (!credentials) {
            return {
                success: false,
                error: 'Invalid demo role',
                code: 'INVALID_DEMO_ROLE'
            };
        }

        return this.login(credentials.username, credentials.password, role);
    }

    /**
     * Get user activity log
     * @param {string} userId - User ID (optional, defaults to current user)
     * @returns {Array} Activity log entries
     */
    getActivityLog(userId = null) {
        const targetUserId = userId || (this.currentUser ? this.currentUser.id : null);
        if (!targetUserId) return [];

        // Check permission
        if (userId && userId !== this.currentUser.id && !this.hasPermission('view_all_profiles')) {
            return [];
        }

        // Mock activity log - in real app this would come from server
        return [
            {
                id: 'activity_001',
                timestamp: new Date().toISOString(),
                action: 'login',
                details: 'User logged in successfully',
                ip: '***********'
            },
            {
                id: 'activity_002',
                timestamp: new Date(Date.now() - 3600000).toISOString(),
                action: 'lesson_completed',
                details: 'Completed lesson: Hello & Goodbye',
                score: 85
            }
        ];
    }

    /**
     * Update user profile
     * @param {Object} updates - Profile updates
     * @returns {Object} Update result
     */
    updateProfile(updates) {
        if (!this.currentUser) {
            return {
                success: false,
                error: 'Not authenticated',
                code: 'NOT_AUTHENTICATED'
            };
        }

        try {
            // Validate updates
            const allowedFields = ['name', 'email', 'preferences', 'avatar'];
            const sanitizedUpdates = {};

            for (const field of allowedFields) {
                if (field in updates) {
                    sanitizedUpdates[field] = this.sanitizeData(updates[field]);
                }
            }

            // Update current user
            Object.assign(this.currentUser, sanitizedUpdates);
            this.currentUser.updatedAt = new Date().toISOString();

            // Save to storage
            this.updateUserData(this.currentUser);
            this.saveSession();

            return {
                success: true,
                message: 'Profile updated successfully',
                user: this.sanitizeUser(this.currentUser)
            };

        } catch (error) {
            console.error('Error updating profile:', error);
            return {
                success: false,
                error: 'Failed to update profile',
                code: 'UPDATE_ERROR'
            };
        }
    }

    /**
     * Change user password
     * @param {string} currentPassword - Current password
     * @param {string} newPassword - New password
     * @returns {Object} Change result
     */
    changePassword(currentPassword, newPassword) {
        if (!this.currentUser) {
            return {
                success: false,
                error: 'Not authenticated',
                code: 'NOT_AUTHENTICATED'
            };
        }

        if (this.currentUser.password !== currentPassword) {
            return {
                success: false,
                error: 'Current password is incorrect',
                code: 'INVALID_PASSWORD'
            };
        }

        if (newPassword.length < 6) {
            return {
                success: false,
                error: 'New password must be at least 6 characters',
                code: 'PASSWORD_TOO_SHORT'
            };
        }

        try {
            this.currentUser.password = newPassword;
            this.currentUser.passwordChangedAt = new Date().toISOString();
            this.updateUserData(this.currentUser);

            return {
                success: true,
                message: 'Password changed successfully'
            };

        } catch (error) {
            console.error('Error changing password:', error);
            return {
                success: false,
                error: 'Failed to change password',
                code: 'CHANGE_ERROR'
            };
        }
    }
}

// Create global instance
window.authManager = new AuthManager();
