/**
 * ENGLISH 2025 - Course Navigator Component
 * Handles course hierarchy display, lesson unlocking logic, and navigation
 */

class CourseNavigator {
    constructor() {
        this.authManager = window.authManager;
        this.dataManager = window.dataManager || new DataManager();
        this.currentUser = null;
        this.currentLevel = 'B1';
        this.courseData = null;
        this.init();
    }

    /**
     * Initialize the course navigator
     */
    async init() {
        try {
            this.currentUser = this.authManager.getCurrentUser();
            if (!this.currentUser) {
                window.location.href = '../index.html';
                return;
            }

            this.currentLevel = this.currentUser.currentLevel || 'A1';
            await this.loadCourseData();
            this.setupEventListeners();
            this.renderLevelTabs();
            this.renderCurrentLevel();
        } catch (error) {
            console.error('Error initializing course navigator:', error);
        }
    }

    /**
     * Load course data from data manager
     */
    async loadCourseData() {
        try {
            // Get course data from mock data
            const mockData = JSON.parse(localStorage.getItem('esl_mock_data'));
            this.courseData = mockData.courses;
        } catch (error) {
            console.error('Error loading course data:', error);
            this.courseData = this.getDefaultCourseData();
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Level tab clicks
        const levelTabs = document.querySelectorAll('.level-tab');
        levelTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                const level = e.target.dataset.level;
                if (!e.target.classList.contains('locked')) {
                    this.switchLevel(level);
                }
            });
        });

        // Unit card clicks
        document.addEventListener('click', (e) => {
            if (e.target.closest('.unit-card') && !e.target.closest('.unit-card').classList.contains('locked')) {
                const unitCard = e.target.closest('.unit-card');
                const unitId = unitCard.dataset.unitId;
                this.openUnitModal(unitId);
            }
        });

        // Lesson clicks
        document.addEventListener('click', (e) => {
            if (e.target.closest('.lesson-item') && !e.target.closest('.lesson-item').classList.contains('locked')) {
                const lessonItem = e.target.closest('.lesson-item');
                const lessonId = lessonItem.dataset.lessonId;
                this.startLesson(lessonId);
            }
        });
    }

    /**
     * Render level tabs with unlock status
     */
    renderLevelTabs() {
        const levelTabs = document.querySelectorAll('.level-tab');
        const unlockedLevels = this.currentUser.unlockedLevels || ['A1'];
        
        levelTabs.forEach(tab => {
            const level = tab.dataset.level;
            
            // Remove existing classes
            tab.classList.remove('active', 'locked');
            
            if (level === this.currentLevel) {
                tab.classList.add('active');
            } else if (!unlockedLevels.includes(level)) {
                tab.classList.add('locked');
            }
        });
    }

    /**
     * Switch to a different level
     */
    switchLevel(level) {
        this.currentLevel = level;
        this.renderLevelTabs();
        this.renderCurrentLevel();
    }

    /**
     * Render current level content
     */
    renderCurrentLevel() {
        const levelData = this.getLevelData(this.currentLevel);
        
        // Update level info
        document.getElementById('level-title').textContent = `${this.currentLevel} - ${levelData.name}`;
        document.getElementById('level-description').textContent = levelData.description;
        
        // Render units
        this.renderUnits(levelData.units);
    }

    /**
     * Render units for current level
     */
    renderUnits(units) {
        const container = document.getElementById('units-container');
        container.innerHTML = '';

        units.forEach((unit, index) => {
            const unitProgress = this.calculateUnitProgress(unit.id);
            const isLocked = this.isUnitLocked(unit.id);
            const status = this.getUnitStatus(unit.id, unitProgress);

            const unitCard = document.createElement('div');
            unitCard.className = `unit-card ${isLocked ? 'locked' : ''}`;
            unitCard.dataset.unitId = unit.id;

            unitCard.innerHTML = `
                <div class="unit-header">
                    <div class="unit-number">${index + 1}</div>
                    <div class="unit-status ${status}">${this.getStatusText(status)}</div>
                </div>
                <h3 class="unit-title">${unit.name}</h3>
                <p class="unit-description">${unit.description || 'Master essential skills and vocabulary for this topic.'}</p>
                <div class="unit-progress">
                    <div class="progress-header">
                        <span>Progress</span>
                        <span>${unitProgress.completed}/${unitProgress.total} lessons</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${unitProgress.percentage}%"></div>
                    </div>
                </div>
                <div class="unit-topics">
                    ${unit.topics ? unit.topics.map(topic => `<span class="topic-tag">${topic}</span>`).join('') : ''}
                </div>
            `;

            container.appendChild(unitCard);
        });
    }

    /**
     * Open unit modal with lesson details
     */
    openUnitModal(unitId) {
        const unitData = this.getUnitData(unitId);
        const lessons = this.getUnitLessons(unitId);
        const unitProgress = this.calculateUnitProgress(unitId);

        // Update modal content
        document.getElementById('modal-unit-title').textContent = unitData.name;
        document.getElementById('unit-progress-text').textContent = `${unitProgress.completed}/${unitProgress.total} lessons`;
        document.getElementById('unit-progress-bar').style.width = `${unitProgress.percentage}%`;

        // Render lessons
        this.renderLessons(lessons, unitId);

        // Render unit exam section
        this.renderUnitExam(unitId, unitProgress);

        // Show modal
        document.getElementById('unit-modal').style.display = 'flex';
    }

    /**
     * Render lessons in modal
     */
    renderLessons(lessons, unitId) {
        const container = document.getElementById('lessons-container');
        container.innerHTML = '';

        lessons.forEach((lesson, index) => {
            const isCompleted = this.isLessonCompleted(lesson.id);
            const isLocked = this.isLessonLocked(lesson.id, index);
            const isCurrent = this.isCurrentLesson(lesson.id);

            const lessonItem = document.createElement('div');
            lessonItem.className = `lesson-item ${isCompleted ? 'completed' : ''} ${isCurrent ? 'current' : ''} ${isLocked ? 'locked' : ''}`;
            lessonItem.dataset.lessonId = lesson.id;

            lessonItem.innerHTML = `
                <div class="lesson-number">${index + 1}</div>
                <div class="lesson-content">
                    <div class="lesson-title">${lesson.title}</div>
                    <div class="lesson-type">${lesson.type || 'Interactive Lesson'}</div>
                </div>
                <div class="lesson-status ${isCompleted ? 'completed' : isCurrent ? 'current' : 'locked'}">
                    ${isCompleted ? 'Completed' : isCurrent ? 'Continue' : isLocked ? 'Locked' : 'Start'}
                </div>
            `;

            container.appendChild(lessonItem);
        });
    }

    /**
     * Render unit exam section
     */
    renderUnitExam(unitId, unitProgress) {
        const container = document.getElementById('unit-exam-section');
        const isExamUnlocked = unitProgress.percentage === 100;
        const isExamCompleted = this.isUnitExamCompleted(unitId);
        const examScore = this.getUnitExamScore(unitId);

        container.innerHTML = `
            <div class="exam-header">
                <span class="exam-icon">📝</span>
                <span class="exam-title">Unit Exam</span>
            </div>
            <div class="exam-description">
                Test your knowledge and skills from this unit. ${this.isLastUnitInLevel(unitId) ? 'Pass this exam to unlock the next level!' : 'Pass to unlock the next unit.'}
            </div>
            <div class="exam-requirements">
                ${isExamUnlocked ? 'All lessons completed. You can now take the exam!' : 'Complete all lessons to unlock the exam.'}
                ${isExamCompleted ? `<br><strong>Your Score: ${examScore}%</strong>` : ''}
            </div>
            <button class="btn ${isExamUnlocked ? 'btn-primary' : 'btn-secondary'}"
                    ${!isExamUnlocked ? 'disabled' : ''}
                    onclick="courseNavigator.takeUnitExam('${unitId}')">
                ${isExamCompleted ? 'Retake Exam' : 'Take Exam'}
            </button>
        `;
    }

    /**
     * Handle unit exam taking
     */
    async takeUnitExam(unitId) {
        this.showNotification('Starting unit exam...', 'info');

        // Simulate exam taking
        setTimeout(async () => {
            const examScore = Math.floor(Math.random() * 30) + 70; // Random score 70-100
            const passed = examScore >= 70;

            if (passed) {
                // Update user progress
                await this.updateUnitExamScore(unitId, examScore);

                // Check if this unlocks next level (Requirement 1.5)
                if (this.isLastUnitInLevel(unitId) && this.allUnitsInLevelCompleted()) {
                    await this.unlockNextLevel();
                    this.showNotification(`Congratulations! You've unlocked the next level!`, 'success');
                } else {
                    this.showNotification(`Exam passed with ${examScore}%! Next unit unlocked.`, 'success');
                }

                // Refresh the display
                this.renderCurrentLevel();
                this.renderLevelTabs();
            } else {
                this.showNotification(`Exam score: ${examScore}%. You need 70% to pass. Try again!`, 'error');
            }

            // Close modal and refresh
            document.getElementById('unit-modal').style.display = 'none';
        }, 2000);
    }

    /**
     * Update unit exam score in user progress
     */
    async updateUnitExamScore(unitId, score) {
        if (!this.currentUser.progress.unitScores) {
            this.currentUser.progress.unitScores = {};
        }

        this.currentUser.progress.unitScores[unitId] = score;

        // Update in data manager
        if (this.dataManager) {
            await this.dataManager.updateStudentProgress(this.currentUser.id, {
                unitScores: this.currentUser.progress.unitScores
            });
        }
    }

    /**
     * Unlock next level when all units completed
     */
    async unlockNextLevel() {
        const levels = ['A1', 'A2', 'B1', 'B2', 'C1'];
        const currentIndex = levels.indexOf(this.currentLevel);

        if (currentIndex < levels.length - 1) {
            const nextLevel = levels[currentIndex + 1];

            if (!this.currentUser.unlockedLevels.includes(nextLevel)) {
                this.currentUser.unlockedLevels.push(nextLevel);
                this.currentUser.currentLevel = nextLevel;
                this.currentLevel = nextLevel;

                // Update in data manager
                if (this.dataManager) {
                    await this.dataManager.updateUser(this.currentUser.id, 'student', {
                        unlockedLevels: this.currentUser.unlockedLevels,
                        currentLevel: nextLevel
                    });
                }
            }
        }
    }

    /**
     * Start a lesson
     */
    startLesson(lessonId) {
        console.log('Starting lesson:', lessonId);
        this.showNotification('Loading lesson...', 'info');

        setTimeout(() => {
            window.location.href = `lesson.html?id=${lessonId}`;
        }, 500);
    }

    /**
     * Helper methods for progress calculation and status checking
     */
    calculateUnitProgress(unitId) {
        const lessons = this.getUnitLessons(unitId);
        const completedLessons = lessons.filter(lesson => this.isLessonCompleted(lesson.id));
        
        return {
            completed: completedLessons.length,
            total: lessons.length,
            percentage: lessons.length > 0 ? Math.round((completedLessons.length / lessons.length) * 100) : 0
        };
    }

    isUnitLocked(unitId) {
        // Check if current level is unlocked
        const unlockedLevels = this.currentUser.unlockedLevels || ['A1'];
        const unitLevel = unitId.split('_')[0];

        if (!unlockedLevels.includes(unitLevel)) {
            return true; // Level not unlocked yet
        }

        // Check if previous units are completed
        const levelData = this.getLevelData(this.currentLevel);
        const unitIndex = levelData.units.findIndex(unit => unit.id === unitId);

        if (unitIndex === 0) return false; // First unit is always unlocked

        // Check if previous unit exam is passed (Requirement 2.7)
        const previousUnit = levelData.units[unitIndex - 1];
        const previousProgress = this.calculateUnitProgress(previousUnit.id);
        const previousExamPassed = this.isUnitExamCompleted(previousUnit.id);

        // Unit is locked if previous unit lessons aren't completed OR exam not passed
        return previousProgress.percentage < 100 || !previousExamPassed;
    }

    isLessonCompleted(lessonId) {
        return this.currentUser.progress.completedLessons.includes(lessonId);
    }

    isLessonLocked(lessonId, lessonIndex) {
        if (lessonIndex === 0) return false; // First lesson is always unlocked
        
        // Check if previous lesson is completed
        const unitLessons = this.getUnitLessons(lessonId.split('_').slice(0, 2).join('_'));
        const previousLesson = unitLessons[lessonIndex - 1];
        return !this.isLessonCompleted(previousLesson.id);
    }

    isCurrentLesson(lessonId) {
        // Simple logic: current lesson is the next uncompleted lesson
        const completedLessons = this.currentUser.progress.completedLessons;
        return !completedLessons.includes(lessonId) && !this.isLessonLocked(lessonId, 0);
    }

    isUnitExamCompleted(unitId) {
        return this.currentUser.progress.unitScores && this.currentUser.progress.unitScores[unitId] >= 70;
    }

    getUnitExamScore(unitId) {
        return this.currentUser.progress.unitScores && this.currentUser.progress.unitScores[unitId] || 0;
    }

    isLastUnitInLevel(unitId) {
        const levelData = this.getLevelData(this.currentLevel);
        const unitIndex = levelData.units.findIndex(unit => unit.id === unitId);
        return unitIndex === levelData.units.length - 1;
    }

    allUnitsInLevelCompleted() {
        const levelData = this.getLevelData(this.currentLevel);
        return levelData.units.every(unit => {
            const progress = this.calculateUnitProgress(unit.id);
            const examPassed = this.isUnitExamCompleted(unit.id);
            return progress.percentage === 100 && examPassed;
        });
    }

    getUnitStatus(unitId, progress) {
        if (progress.percentage === 100) return 'completed';
        if (progress.percentage > 0) return 'in-progress';
        if (this.isUnitLocked(unitId)) return 'locked';
        return 'available';
    }

    getStatusText(status) {
        const statusTexts = {
            'completed': 'Completed',
            'in-progress': 'In Progress',
            'locked': 'Locked',
            'available': 'Available'
        };
        return statusTexts[status] || 'Available';
    }

    /**
     * Data helper methods
     */
    getLevelData(level) {
        if (this.courseData && this.courseData.levels) {
            return this.courseData.levels.find(l => l.id === level) || this.getDefaultLevelData(level);
        }
        return this.getDefaultLevelData(level);
    }

    getUnitData(unitId) {
        const level = unitId.split('_')[0];
        const levelData = this.getLevelData(level);
        return levelData.units.find(unit => unit.id === unitId);
    }

    getUnitLessons(unitId) {
        // Generate mock lessons for the unit
        const lessonCount = 4;
        const lessons = [];
        
        for (let i = 1; i <= lessonCount; i++) {
            lessons.push({
                id: `${unitId}_Lesson${i}`,
                title: `Lesson ${i}: ${this.getLessonTitle(i)}`,
                type: this.getLessonType(i),
                duration: 30
            });
        }
        
        return lessons;
    }

    getLessonTitle(lessonNumber) {
        const titles = [
            'Vocabulary Building',
            'Grammar Focus',
            'Conversation Practice',
            'Listening Comprehension'
        ];
        return titles[lessonNumber - 1] || `Lesson ${lessonNumber}`;
    }

    getLessonType(lessonNumber) {
        const types = ['vocabulary', 'grammar', 'conversation', 'listening'];
        return types[lessonNumber - 1] || 'interactive';
    }

    getDefaultLevelData(level) {
        const levelInfo = {
            'A1': { name: 'Beginner', description: 'Basic English for everyday situations' },
            'A2': { name: 'Elementary', description: 'Build foundation skills for common topics' },
            'B1': { name: 'Lower Intermediate', description: 'Develop confidence in everyday conversations' },
            'B2': { name: 'Upper Intermediate', description: 'Express ideas clearly on complex topics' },
            'C1': { name: 'Advanced', description: 'Master sophisticated language use' }
        };

        return {
            id: level,
            name: levelInfo[level]?.name || level,
            description: levelInfo[level]?.description || 'Advanced English skills',
            units: this.generateDefaultUnits(level)
        };
    }

    generateDefaultUnits(level) {
        const unitTopics = {
            'A1': ['Greetings & Introductions', 'Daily Activities', 'Food & Shopping', 'Time & Numbers'],
            'A2': ['Family & Friends', 'Work & Study', 'Health & Body', 'Travel & Transport'],
            'B1': ['Hobbies & Interests', 'Technology & Media', 'Environment & Nature', 'Culture & Society'],
            'B2': ['Business & Career', 'Science & Innovation', 'Arts & Literature', 'Global Issues'],
            'C1': ['Academic Writing', 'Professional Communication', 'Critical Analysis', 'Advanced Grammar']
        };

        const topics = unitTopics[level] || unitTopics['B1'];
        
        return topics.map((topic, index) => ({
            id: `${level}_Unit${index + 1}`,
            name: topic,
            description: `Master essential skills and vocabulary for ${topic.toLowerCase()}.`,
            lessons: 4,
            duration: '1 week',
            topics: this.generateUnitTopics(topic)
        }));
    }

    generateUnitTopics(unitName) {
        // Generate relevant sub-topics based on unit name
        const topicMap = {
            'Greetings & Introductions': ['Hello & Goodbye', 'Personal Info', 'Countries', 'Jobs'],
            'Daily Activities': ['Routines', 'Time', 'Food', 'Shopping'],
            'Food & Shopping': ['Restaurants', 'Grocery', 'Prices', 'Preferences'],
            'Time & Numbers': ['Clock Time', 'Dates', 'Counting', 'Money']
        };
        
        return topicMap[unitName] || ['Vocabulary', 'Grammar', 'Speaking', 'Listening'];
    }

    getDefaultCourseData() {
        return {
            levels: ['A1', 'A2', 'B1', 'B2', 'C1'].map(level => this.getDefaultLevelData(level))
        };
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        // Simple notification system
        console.log(`[${type.toUpperCase()}] ${message}`);
        
        // In a real app, this would show a proper notification UI
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 24px;
            background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
            color: white;
            border-radius: 8px;
            z-index: 10000;
            animation: slideIn 0.3s ease-out;
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// Make CourseNavigator available globally
window.CourseNavigator = CourseNavigator;
