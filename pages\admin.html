<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Portal - ENGLISH 2025</title>
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/admin.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="portal-header">
            <div class="container">
                <div class="header-content">
                    <div class="logo">
                        <h1>ENGLISH 2025</h1>
                        <span class="portal-type">Admin Portal</span>
                    </div>
                    <nav class="portal-nav">
                        <a href="#dashboard" class="nav-link active">Dashboard</a>
                        <a href="#users" class="nav-link">Users</a>
                        <a href="#analytics" class="nav-link">Analytics</a>
                        <a href="#content" class="nav-link">Content</a>
                        <a href="#settings" class="nav-link">Settings</a>
                    </nav>
                    <div class="user-menu">
                        <div class="user-avatar">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=387&q=80" alt="Admin Avatar">
                        </div>
                        <span class="user-name">Michael Chen</span>
                        <button class="logout-btn" onclick="logout()">Logout</button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <div class="container">
                <!-- Welcome Section -->
                <section class="welcome-section">
                    <div class="welcome-content">
                        <h2>System Overview</h2>
                        <p>Monitor platform health, user activity, and business metrics in real-time.</p>
                    </div>
                    <div class="system-status">
                        <div class="status-indicator online">
                            <div class="status-dot"></div>
                            <span>All Systems Operational</span>
                        </div>
                    </div>
                </section>

                <!-- Key Metrics -->
                <section class="metrics-section">
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-icon">👥</div>
                            <div class="metric-content">
                                <div class="metric-number">1,247</div>
                                <div class="metric-label">Active Users</div>
                                <div class="metric-change positive">+12% this month</div>
                            </div>
                        </div>
                        
                        <div class="metric-card">
                            <div class="metric-icon">💰</div>
                            <div class="metric-content">
                                <div class="metric-number">$24,580</div>
                                <div class="metric-label">Monthly Revenue</div>
                                <div class="metric-change positive">+8% this month</div>
                            </div>
                        </div>
                        
                        <div class="metric-card">
                            <div class="metric-icon">📚</div>
                            <div class="metric-content">
                                <div class="metric-number">3,456</div>
                                <div class="metric-label">Lessons Completed</div>
                                <div class="metric-change positive">+15% this week</div>
                            </div>
                        </div>
                        
                        <div class="metric-card">
                            <div class="metric-icon">🎯</div>
                            <div class="metric-content">
                                <div class="metric-number">94.2%</div>
                                <div class="metric-label">Student Satisfaction</div>
                                <div class="metric-change positive">+2% this month</div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Dashboard Grid -->
                <div class="dashboard-grid">
                    <!-- User Management -->
                    <div class="dashboard-card users-card">
                        <h3>User Management</h3>
                        <div class="user-stats">
                            <div class="user-type">
                                <div class="user-count">1,156</div>
                                <div class="user-label">Students</div>
                            </div>
                            <div class="user-type">
                                <div class="user-count">45</div>
                                <div class="user-label">Teachers</div>
                            </div>
                            <div class="user-type">
                                <div class="user-count">8</div>
                                <div class="user-label">Admins</div>
                            </div>
                        </div>
                        <div class="recent-users">
                            <h4>Recent Registrations</h4>
                            <div class="user-list">
                                <div class="user-item">
                                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=387&q=80" alt="User">
                                    <div class="user-info">
                                        <div class="user-name">John Smith</div>
                                        <div class="user-role">Student</div>
                                    </div>
                                    <div class="user-date">2 hours ago</div>
                                </div>
                                <div class="user-item">
                                    <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=387&q=80" alt="User">
                                    <div class="user-info">
                                        <div class="user-name">Emma Wilson</div>
                                        <div class="user-role">Teacher</div>
                                    </div>
                                    <div class="user-date">1 day ago</div>
                                </div>
                            </div>
                        </div>
                        <button class="btn btn-outline">Manage Users</button>
                    </div>

                    <!-- Revenue Analytics -->
                    <div class="dashboard-card revenue-card">
                        <h3>Revenue Analytics</h3>
                        <div class="revenue-chart">
                            <div class="chart-placeholder">
                                📈 Revenue Trend Chart
                            </div>
                        </div>
                        <div class="revenue-breakdown">
                            <div class="revenue-item">
                                <span class="revenue-source">Subscriptions</span>
                                <span class="revenue-amount">$18,450</span>
                            </div>
                            <div class="revenue-item">
                                <span class="revenue-source">One-time Payments</span>
                                <span class="revenue-amount">$4,230</span>
                            </div>
                            <div class="revenue-item">
                                <span class="revenue-source">Corporate Training</span>
                                <span class="revenue-amount">$1,900</span>
                            </div>
                        </div>
                    </div>

                    <!-- Teacher Performance -->
                    <div class="dashboard-card teachers-card">
                        <h3>Teacher Performance</h3>
                        <div class="teacher-list">
                            <div class="teacher-item">
                                <img src="https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=387&q=80" alt="Teacher">
                                <div class="teacher-info">
                                    <div class="teacher-name">Sarah Johnson</div>
                                    <div class="teacher-stats">
                                        <span>23 students</span>
                                        <span>4.9★</span>
                                    </div>
                                </div>
                                <div class="teacher-workload">
                                    <div class="workload-bar">
                                        <div class="workload-fill" style="width: 85%"></div>
                                    </div>
                                    <span>85% capacity</span>
                                </div>
                            </div>
                            
                            <div class="teacher-item">
                                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=387&q=80" alt="Teacher">
                                <div class="teacher-info">
                                    <div class="teacher-name">David Rodriguez</div>
                                    <div class="teacher-stats">
                                        <span>18 students</span>
                                        <span>4.7★</span>
                                    </div>
                                </div>
                                <div class="teacher-workload">
                                    <div class="workload-bar">
                                        <div class="workload-fill" style="width: 65%"></div>
                                    </div>
                                    <span>65% capacity</span>
                                </div>
                            </div>
                            
                            <div class="teacher-item">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=387&q=80" alt="Teacher">
                                <div class="teacher-info">
                                    <div class="teacher-name">Lisa Chen</div>
                                    <div class="teacher-stats">
                                        <span>21 students</span>
                                        <span>4.8★</span>
                                    </div>
                                </div>
                                <div class="teacher-workload">
                                    <div class="workload-bar">
                                        <div class="workload-fill" style="width: 75%"></div>
                                    </div>
                                    <span>75% capacity</span>
                                </div>
                            </div>
                        </div>
                        <button class="btn btn-outline">View All Teachers</button>
                    </div>

                    <!-- System Health -->
                    <div class="dashboard-card system-card">
                        <h3>System Health</h3>
                        <div class="health-metrics">
                            <div class="health-item">
                                <div class="health-label">Server Uptime</div>
                                <div class="health-value good">99.9%</div>
                            </div>
                            <div class="health-item">
                                <div class="health-label">Response Time</div>
                                <div class="health-value good">245ms</div>
                            </div>
                            <div class="health-item">
                                <div class="health-label">Error Rate</div>
                                <div class="health-value good">0.02%</div>
                            </div>
                            <div class="health-item">
                                <div class="health-label">Active Sessions</div>
                                <div class="health-value">1,247</div>
                            </div>
                        </div>
                        <div class="alerts-section">
                            <h4>Recent Alerts</h4>
                            <div class="alert-item">
                                <div class="alert-icon">⚠️</div>
                                <div class="alert-text">High server load detected</div>
                                <div class="alert-time">2 hours ago</div>
                            </div>
                            <div class="alert-item resolved">
                                <div class="alert-icon">✅</div>
                                <div class="alert-text">Database backup completed</div>
                                <div class="alert-time">4 hours ago</div>
                            </div>
                        </div>
                    </div>

                    <!-- Support Tickets -->
                    <div class="dashboard-card support-card">
                        <h3>Support Tickets</h3>
                        <div class="ticket-stats">
                            <div class="ticket-stat">
                                <div class="ticket-number">12</div>
                                <div class="ticket-label">Open</div>
                            </div>
                            <div class="ticket-stat">
                                <div class="ticket-number">8</div>
                                <div class="ticket-label">In Progress</div>
                            </div>
                            <div class="ticket-stat">
                                <div class="ticket-number">45</div>
                                <div class="ticket-label">Resolved Today</div>
                            </div>
                        </div>
                        <div class="recent-tickets">
                            <h4>Recent Tickets</h4>
                            <div class="ticket-list">
                                <div class="ticket-item">
                                    <div class="ticket-priority high">High</div>
                                    <div class="ticket-info">
                                        <div class="ticket-title">Audio not working in lessons</div>
                                        <div class="ticket-user">John Doe</div>
                                    </div>
                                    <div class="ticket-time">30 min ago</div>
                                </div>
                                <div class="ticket-item">
                                    <div class="ticket-priority medium">Medium</div>
                                    <div class="ticket-info">
                                        <div class="ticket-title">Cannot schedule classes</div>
                                        <div class="ticket-user">Maria Garcia</div>
                                    </div>
                                    <div class="ticket-time">2 hours ago</div>
                                </div>
                            </div>
                        </div>
                        <button class="btn btn-primary">View All Tickets</button>
                    </div>

                    <!-- Quick Actions -->
                    <div class="dashboard-card actions-card">
                        <h3>Quick Actions</h3>
                        <div class="admin-actions">
                            <button class="action-btn">
                                <span class="action-icon">👤</span>
                                <span>Add New User</span>
                            </button>
                            <button class="action-btn">
                                <span class="action-icon">📊</span>
                                <span>Generate Report</span>
                            </button>
                            <button class="action-btn">
                                <span class="action-icon">📧</span>
                                <span>Send Announcement</span>
                            </button>
                            <button class="action-btn">
                                <span class="action-icon">⚙️</span>
                                <span>System Settings</span>
                            </button>
                            <button class="action-btn">
                                <span class="action-icon">💾</span>
                                <span>Backup Data</span>
                            </button>
                            <button class="action-btn">
                                <span class="action-icon">🔄</span>
                                <span>Update Content</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="../js/data/storage.js"></script>
    <script src="../js/data/mockData.js"></script>
    <script src="../js/auth.js"></script>
    <script src="../js/admin/adminApp.js"></script>
    <script>
        function logout() {
            if (window.authManager) {
                window.authManager.logout();
            }
            window.location.href = '../index.html';
        }

        // Initialize admin app
        const adminApp = new AdminApp();
    </script>
</body>
</html>
