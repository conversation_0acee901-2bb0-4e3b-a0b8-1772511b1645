/**
 * ENGLISH 2025 - Progress Tracker Component
 * Handles progress visualization and analytics for student learning journey
 */

class ProgressTracker {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            animationDuration: 1000,
            showSkillBreakdown: true,
            showLevelProgression: true,
            theme: 'default',
            ...options
        };
        this.data = null;
        this.init();
    }

    /**
     * Initialize the progress tracker
     */
    init() {
        if (!this.container) {
            console.error('Progress tracker container not found');
            return;
        }
        
        this.container.className = `progress-tracker ${this.options.theme}`;
        this.setupEventListeners();
    }

    /**
     * Update progress data and refresh visualization
     */
    updateProgress(studentData) {
        this.data = studentData;
        this.render();
    }

    /**
     * Render the complete progress visualization
     */
    render() {
        if (!this.data) return;

        this.container.innerHTML = '';
        
        // Create main sections
        const sections = [
            this.createOverallProgressSection(),
            this.createLevelProgressSection(),
            this.createSkillsSection(),
            this.createAchievementsSection()
        ];

        sections.forEach(section => {
            if (section) {
                this.container.appendChild(section);
            }
        });

        // Trigger animations
        setTimeout(() => this.animateElements(), 100);
    }

    /**
     * Create overall progress section
     */
    createOverallProgressSection() {
        const section = document.createElement('div');
        section.className = 'progress-section overall-progress';
        
        const progress = this.data.progress.overallProgress;
        
        section.innerHTML = `
            <h3>Overall Progress</h3>
            <div class="circular-progress" data-progress="${progress}">
                <div class="progress-circle">
                    <div class="progress-text">
                        <span class="progress-value">${progress}%</span>
                        <span class="progress-label">Complete</span>
                    </div>
                </div>
            </div>
            <div class="progress-stats">
                <div class="stat">
                    <span class="stat-value">${this.data.progress.completedLessons.length}</span>
                    <span class="stat-label">Lessons Completed</span>
                </div>
                <div class="stat">
                    <span class="stat-value">${this.data.studyStreak}</span>
                    <span class="stat-label">Day Streak</span>
                </div>
            </div>
        `;

        return section;
    }

    /**
     * Create level progression section
     */
    createLevelProgressSection() {
        if (!this.options.showLevelProgression) return null;

        const section = document.createElement('div');
        section.className = 'progress-section level-progression';
        
        const levels = ['A1', 'A2', 'B1', 'B2', 'C1'];
        const currentLevelIndex = levels.indexOf(this.data.currentLevel);
        
        section.innerHTML = `
            <h3>Level Progression</h3>
            <div class="level-timeline">
                ${levels.map((level, index) => `
                    <div class="level-milestone ${this.getLevelStatus(index, currentLevelIndex)}">
                        <div class="milestone-marker">
                            <span class="level-code">${level}</span>
                        </div>
                        <div class="milestone-label">${this.getLevelName(level)}</div>
                    </div>
                `).join('')}
            </div>
        `;

        return section;
    }

    /**
     * Create skills breakdown section
     */
    createSkillsSection() {
        if (!this.options.showSkillBreakdown || !this.data.progress.skillsAssessment) return null;

        const section = document.createElement('div');
        section.className = 'progress-section skills-breakdown';
        
        const skills = this.data.progress.skillsAssessment;
        
        section.innerHTML = `
            <h3>Skills Assessment</h3>
            <div class="skills-grid">
                ${Object.entries(skills).map(([skill, score]) => `
                    <div class="skill-card">
                        <div class="skill-header">
                            <span class="skill-name">${this.capitalizeFirst(skill)}</span>
                            <span class="skill-score">${score}%</span>
                        </div>
                        <div class="skill-bar">
                            <div class="skill-fill" data-width="${score}%"></div>
                        </div>
                        <div class="skill-level">${this.getSkillLevel(score)}</div>
                    </div>
                `).join('')}
            </div>
        `;

        return section;
    }

    /**
     * Create achievements section
     */
    createAchievementsSection() {
        const section = document.createElement('div');
        section.className = 'progress-section achievements';
        
        const achievements = this.generateAchievements();
        
        section.innerHTML = `
            <h3>Recent Achievements</h3>
            <div class="achievements-list">
                ${achievements.map(achievement => `
                    <div class="achievement-item ${achievement.type}">
                        <div class="achievement-icon">${achievement.icon}</div>
                        <div class="achievement-content">
                            <div class="achievement-title">${achievement.title}</div>
                            <div class="achievement-description">${achievement.description}</div>
                        </div>
                        <div class="achievement-date">${achievement.date}</div>
                    </div>
                `).join('')}
            </div>
        `;

        return section;
    }

    /**
     * Animate progress elements
     */
    animateElements() {
        // Animate circular progress
        const circularProgress = this.container.querySelector('.circular-progress');
        if (circularProgress) {
            this.animateCircularProgress(circularProgress);
        }

        // Animate skill bars
        const skillFills = this.container.querySelectorAll('.skill-fill');
        skillFills.forEach((fill, index) => {
            setTimeout(() => {
                fill.style.width = fill.dataset.width;
            }, index * 200);
        });

        // Animate level milestones
        const milestones = this.container.querySelectorAll('.level-milestone');
        milestones.forEach((milestone, index) => {
            setTimeout(() => {
                milestone.classList.add('animated');
            }, index * 150);
        });
    }

    /**
     * Animate circular progress indicator
     */
    animateCircularProgress(element) {
        const progress = parseInt(element.dataset.progress);
        const circumference = 2 * Math.PI * 45; // radius = 45
        const offset = circumference - (progress / 100) * circumference;
        
        // Create SVG circle if not exists
        if (!element.querySelector('svg')) {
            const svg = document.createElement('div');
            svg.innerHTML = `
                <svg class="progress-ring" width="100" height="100">
                    <circle class="progress-ring-background" cx="50" cy="50" r="45"></circle>
                    <circle class="progress-ring-fill" cx="50" cy="50" r="45" 
                            stroke-dasharray="${circumference}" 
                            stroke-dashoffset="${circumference}"></circle>
                </svg>
            `;
            element.querySelector('.progress-circle').appendChild(svg.firstElementChild);
        }

        // Animate the circle
        const circle = element.querySelector('.progress-ring-fill');
        setTimeout(() => {
            circle.style.strokeDashoffset = offset;
        }, 500);
    }

    /**
     * Helper methods
     */
    getLevelStatus(index, currentIndex) {
        if (index < currentIndex) return 'completed';
        if (index === currentIndex) return 'current';
        return 'locked';
    }

    getLevelName(levelCode) {
        const names = {
            'A1': 'Beginner',
            'A2': 'Elementary',
            'B1': 'Intermediate',
            'B2': 'Upper Intermediate',
            'C1': 'Advanced'
        };
        return names[levelCode] || levelCode;
    }

    getSkillLevel(score) {
        if (score >= 90) return 'Excellent';
        if (score >= 80) return 'Good';
        if (score >= 70) return 'Fair';
        if (score >= 60) return 'Needs Work';
        return 'Beginner';
    }

    capitalizeFirst(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    }

    generateAchievements() {
        // Mock achievements based on user data
        return [
            {
                type: 'milestone',
                icon: '🏆',
                title: 'Level Completed',
                description: 'Completed A2 Elementary level',
                date: '2 days ago'
            },
            {
                type: 'streak',
                icon: '🔥',
                title: 'Study Streak',
                description: `${this.data.studyStreak} days in a row`,
                date: 'Today'
            },
            {
                type: 'skill',
                icon: '⭐',
                title: 'Perfect Score',
                description: 'Scored 100% in vocabulary quiz',
                date: '1 week ago'
            }
        ];
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Add any interactive features here
    }

    /**
     * Export progress data
     */
    exportProgress() {
        return {
            studentId: this.data.id,
            overallProgress: this.data.progress.overallProgress,
            currentLevel: this.data.currentLevel,
            skillsAssessment: this.data.progress.skillsAssessment,
            completedLessons: this.data.progress.completedLessons.length,
            studyStreak: this.data.studyStreak,
            exportDate: new Date().toISOString()
        };
    }
}

// Make ProgressTracker available globally
window.ProgressTracker = ProgressTracker;
