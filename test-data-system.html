<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESL Platform - Data System Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background-color: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #5a6268;
        }
        
        .console-output {
            background-color: #1e1e1e;
            color: #d4d4d4;
            padding: 20px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .data-preview {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .data-item {
            margin-bottom: 10px;
            padding: 10px;
            background-color: white;
            border-radius: 4px;
            border-left: 4px solid #667eea;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
            
            .test-controls {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎓 ESL Learning Platform</h1>
        <h2>Data Management System Test</h2>
        <p>Testing comprehensive mock data system and storage management</p>
    </div>

    <div class="test-section">
        <h3>🧪 Test Controls</h3>
        <div class="test-controls">
            <button class="btn btn-primary" onclick="runTests()">Run All Tests</button>
            <button class="btn btn-secondary" onclick="clearConsole()">Clear Console</button>
            <button class="btn btn-secondary" onclick="showDataPreview()">Preview Data</button>
            <button class="btn btn-secondary" onclick="showSystemHealth()">System Health</button>
        </div>
    </div>

    <div class="grid">
        <div class="test-section">
            <h3>📊 Test Results</h3>
            <div id="console-output" class="console-output">
                Click "Run All Tests" to start testing the data management system...
            </div>
        </div>

        <div class="test-section">
            <h3>📋 Data Preview</h3>
            <div id="data-preview" class="data-preview">
                <p>Click "Preview Data" to see sample data...</p>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h3>🔧 System Information</h3>
        <div id="system-info" class="data-preview">
            <div class="data-item">
                <span class="status-indicator status-success"></span>
                <strong>Storage Manager:</strong> <span id="storage-status">Loading...</span>
            </div>
            <div class="data-item">
                <span class="status-indicator status-success"></span>
                <strong>Mock Data Manager:</strong> <span id="mock-data-status">Loading...</span>
            </div>
            <div class="data-item">
                <span class="status-indicator status-success"></span>
                <strong>Data Manager:</strong> <span id="data-manager-status">Loading...</span>
            </div>
        </div>
    </div>

    <!-- Load our data management scripts -->
    <script src="js/data/storage.js"></script>
    <script src="js/data/mockData.js"></script>
    <script src="js/data/dataManager.js"></script>
    <script src="js/tests/dataManagerTest.js"></script>

    <script>
        // Override console.log to capture output
        const originalConsoleLog = console.log;
        const consoleOutput = document.getElementById('console-output');
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            const message = args.join(' ');
            consoleOutput.textContent += message + '\n';
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        };

        // Test functions
        async function runTests() {
            clearConsole();
            console.log('🚀 Starting comprehensive data management tests...\n');
            
            try {
                const tester = new DataManagerTest();
                await tester.runAllTests();
            } catch (error) {
                console.log('❌ Error running tests:', error.message);
            }
        }

        function clearConsole() {
            consoleOutput.textContent = '';
        }

        function showDataPreview() {
            const preview = document.getElementById('data-preview');
            
            try {
                const students = window.dataManager.mockDataManager.getData('students');
                const teachers = window.dataManager.mockDataManager.getData('teachers');
                const courses = window.dataManager.mockDataManager.getData('courses');
                
                preview.innerHTML = `
                    <div class="data-item">
                        <strong>Students:</strong> ${students ? students.length : 0} records
                    </div>
                    <div class="data-item">
                        <strong>Teachers:</strong> ${teachers ? teachers.length : 0} records
                    </div>
                    <div class="data-item">
                        <strong>Course Levels:</strong> ${courses && courses.levels ? courses.levels.length : 0} levels
                    </div>
                    <div class="data-item">
                        <strong>Sample Student:</strong> ${students && students[0] ? students[0].name + ' (' + students[0].currentLevel + ')' : 'None'}
                    </div>
                `;
            } catch (error) {
                preview.innerHTML = `<p style="color: red;">Error loading data preview: ${error.message}</p>`;
            }
        }

        function showSystemHealth() {
            try {
                const health = window.dataManager.getSystemHealth();
                const preview = document.getElementById('data-preview');
                
                preview.innerHTML = `
                    <div class="data-item">
                        <strong>System Status:</strong> ${health.initialized ? '✅ Initialized' : '❌ Not Initialized'}
                    </div>
                    <div class="data-item">
                        <strong>Storage Usage:</strong> ${Math.round(health.storage.appSize / 1024)} KB / ${Math.round(health.storage.quota / 1024)} KB
                    </div>
                    <div class="data-item">
                        <strong>Data Integrity:</strong> ${health.dataIntegrity.valid ? '✅ Valid' : '❌ Issues Found'}
                    </div>
                    <div class="data-item">
                        <strong>Total Items:</strong> ${health.storage.totalItems}
                    </div>
                `;
            } catch (error) {
                document.getElementById('data-preview').innerHTML = `<p style="color: red;">Error getting system health: ${error.message}</p>`;
            }
        }

        // Initialize status indicators
        window.addEventListener('load', () => {
            setTimeout(() => {
                document.getElementById('storage-status').textContent = window.storageManager ? 'Ready' : 'Error';
                document.getElementById('mock-data-status').textContent = window.MockDataManager ? 'Ready' : 'Error';
                document.getElementById('data-manager-status').textContent = window.dataManager ? 'Ready' : 'Error';
                
                // Update status indicators
                const indicators = document.querySelectorAll('.status-indicator');
                indicators.forEach((indicator, index) => {
                    const isReady = [window.storageManager, window.MockDataManager, window.dataManager][index];
                    indicator.className = `status-indicator ${isReady ? 'status-success' : 'status-error'}`;
                });
            }, 500);
        });
    </script>
</body>
</html>
