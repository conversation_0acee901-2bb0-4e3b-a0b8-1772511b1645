/**
 * ENGLISH 2025 - Main Application Controller
 * Handles role selection, navigation, and core app functionality
 */

class App {
    constructor() {
        this.authManager = window.authManager;
        this.currentUser = null;
        this.loadingOverlay = document.getElementById('loading-overlay');
        this.init();
    }

    /**
     * Initialize the application
     */
    init() {
        this.setupEventListeners();
        this.checkExistingSession();
        this.animateOnLoad();
        this.setupAuthenticationListeners();
    }

    /**
     * Set up event listeners for role selection and navigation
     */
    setupEventListeners() {
        // Role selection buttons
        const roleButtons = document.querySelectorAll('.role-button');
        roleButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const role = e.target.getAttribute('data-role');
                this.handleRoleSelection(role);
            });
        });

        // Role cards - make entire card clickable
        const roleCards = document.querySelectorAll('.role-card');
        roleCards.forEach(card => {
            card.addEventListener('click', (e) => {
                // Don't trigger if clicking the button directly
                if (e.target.classList.contains('role-button')) return;
                
                const role = card.getAttribute('data-role');
                this.handleRoleSelection(role);
            });

            // Add hover effects
            card.addEventListener('mouseenter', () => {
                card.style.cursor = 'pointer';
            });
        });

        // Smooth scrolling for navigation links
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                const href = link.getAttribute('href');
                if (href.startsWith('#')) {
                    e.preventDefault();
                    this.smoothScrollTo(href);
                }
            });
        });

        // Handle browser back/forward buttons
        window.addEventListener('popstate', (e) => {
            if (e.state && e.state.role) {
                this.navigateToPortal(e.state.role, false);
            }
        });

        // Handle modal overlay clicks
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.closeLoginModal();
            }
        });

        // Handle escape key to close modal
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeLoginModal();
            }
        });
    }

    /**
     * Handle role selection and show login modal
     * @param {string} role - The selected role (student, teacher, admin)
     */
    handleRoleSelection(role) {
        if (!['student', 'teacher', 'admin'].includes(role)) {
            console.error('Invalid role selected:', role);
            return;
        }

        this.selectedRole = role;
        this.showLoginModal(role);
    }

    /**
     * Navigate to the appropriate portal based on role
     * @param {string} role - The role to navigate to
     * @param {boolean} pushState - Whether to push state to history
     */
    navigateToPortal(role, pushState = true) {
        const portalUrls = {
            student: 'pages/student.html',
            teacher: 'pages/teacher.html',
            admin: 'pages/admin.html'
        };

        const url = portalUrls[role];
        if (!url) {
            console.error('No portal URL found for role:', role);
            this.hideLoading();
            return;
        }

        // Store role selection in session storage
        sessionStorage.setItem('selectedRole', role);
        sessionStorage.setItem('lastAccess', new Date().toISOString());

        // Add to browser history
        if (pushState) {
            history.pushState({ role }, `${role} Portal`, url);
        }

        // Navigate to portal
        window.location.href = url;
    }

    /**
     * Check for existing session and redirect if appropriate
     */
    checkExistingSession() {
        if (this.authManager && this.authManager.isAuthenticated()) {
            const user = this.authManager.getCurrentUser();
            if (user) {
                this.showContinueSessionOption(user.role, user.name);
            }
        }
    }

    /**
     * Setup authentication event listeners
     */
    setupAuthenticationListeners() {
        // Listen for logout events
        window.addEventListener('userLogout', () => {
            this.handleLogout();
        });

        // Listen for authentication changes
        window.addEventListener('authenticationChanged', (event) => {
            this.handleAuthenticationChange(event.detail);
        });
    }

    /**
     * Show option to continue previous session
     * @param {string} role - The previous role
     * @param {string} userName - The user's name
     */
    showContinueSessionOption(role, userName = '') {
        const heroContent = document.querySelector('.hero-content');
        const continueDiv = document.createElement('div');
        continueDiv.className = 'continue-session';
        const displayName = userName ? ` (${userName})` : '';
        continueDiv.innerHTML = `
            <div class="continue-session-card">
                <p>Continue your previous session as <strong>${role}${displayName}</strong>?</p>
                <div class="continue-session-buttons">
                    <button class="btn btn-primary" onclick="app.navigateToPortal('${role}')">
                        Continue Session
                    </button>
                    <button class="btn btn-outline" onclick="app.dismissContinueSession()">
                        Start Fresh
                    </button>
                </div>
            </div>
        `;

        // Insert before role selection
        const roleSelection = document.querySelector('.role-selection');
        if (roleSelection && heroContent) {
            heroContent.insertBefore(continueDiv, roleSelection);
        }

        // Add styles for continue session card
        this.addContinueSessionStyles();
    }

    /**
     * Dismiss the continue session option
     */
    dismissContinueSession() {
        const continueSession = document.querySelector('.continue-session');
        if (continueSession) {
            continueSession.remove();
        }

        // Logout current user
        if (this.authManager) {
            this.authManager.logout();
        }
    }

    /**
     * Handle logout event
     */
    handleLogout() {
        this.currentUser = null;
        this.dismissContinueSession();

        // Show notification
        this.showNotification('Logged out successfully', 'info');
    }

    /**
     * Handle authentication change
     * @param {Object} authData - Authentication data
     */
    handleAuthenticationChange(authData) {
        if (authData.authenticated) {
            this.currentUser = authData.user;
        } else {
            this.currentUser = null;
        }
    }

    /**
     * Show login modal for specific role
     * @param {string} role - The role to login as
     */
    showLoginModal(role) {
        const modal = document.getElementById('login-modal');
        const title = document.getElementById('login-title');
        const errorDiv = document.getElementById('login-error');

        if (modal && title) {
            title.textContent = `Login to ${role.charAt(0).toUpperCase() + role.slice(1)} Portal`;
            modal.classList.remove('hidden');

            // Clear any previous errors
            if (errorDiv) {
                errorDiv.classList.add('hidden');
                errorDiv.textContent = '';
            }

            // Focus on username field
            const usernameField = document.getElementById('username');
            if (usernameField) {
                setTimeout(() => usernameField.focus(), 100);
            }
        }
    }

    /**
     * Close login modal
     */
    closeLoginModal() {
        const modal = document.getElementById('login-modal');
        if (modal) {
            modal.classList.add('hidden');
        }
        this.selectedRole = null;
    }

    /**
     * Handle login form submission
     * @param {Event} event - Form submit event
     */
    async handleLogin(event) {
        event.preventDefault();

        const form = event.target;
        const formData = new FormData(form);
        const username = formData.get('username');
        const password = formData.get('password');
        const errorDiv = document.getElementById('login-error');

        if (!this.selectedRole) {
            this.showError(errorDiv, 'No role selected');
            return;
        }

        // Show loading
        this.showLoading();

        try {
            const result = await this.authManager.login(username, password, this.selectedRole);

            if (result.success) {
                this.closeLoginModal();
                this.hideLoading();
                this.showNotification('Login successful!', 'success');

                // Navigate to portal
                setTimeout(() => {
                    this.navigateToPortal(this.selectedRole);
                }, 500);
            } else {
                this.hideLoading();
                this.showError(errorDiv, result.error || 'Login failed');
            }
        } catch (error) {
            this.hideLoading();
            this.showError(errorDiv, 'An error occurred during login');
            console.error('Login error:', error);
        }
    }

    /**
     * Handle demo login
     */
    async demoLogin() {
        if (!this.selectedRole) {
            return;
        }

        this.showLoading();

        try {
            const result = await this.authManager.demoLogin(this.selectedRole);

            if (result.success) {
                this.closeLoginModal();
                this.hideLoading();
                this.showNotification(`Demo login successful as ${this.selectedRole}!`, 'success');

                // Navigate to portal
                setTimeout(() => {
                    this.navigateToPortal(this.selectedRole);
                }, 500);
            } else {
                this.hideLoading();
                const errorDiv = document.getElementById('login-error');
                this.showError(errorDiv, result.error || 'Demo login failed');
            }
        } catch (error) {
            this.hideLoading();
            const errorDiv = document.getElementById('login-error');
            this.showError(errorDiv, 'An error occurred during demo login');
            console.error('Demo login error:', error);
        }
    }

    /**
     * Show error message
     * @param {Element} errorDiv - Error div element
     * @param {string} message - Error message
     */
    showError(errorDiv, message) {
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.classList.remove('hidden');
        }
    }

    /**
     * Show notification
     * @param {string} message - Notification message
     * @param {string} type - Notification type (info, success, warning, error)
     */
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-message">${message}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">&times;</button>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    /**
     * Add styles for continue session card
     */
    addContinueSessionStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .continue-session {
                margin-bottom: 2rem;
            }
            .continue-session-card {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(10px);
                border-radius: 1rem;
                padding: 1.5rem;
                text-align: center;
                color: var(--gray-800);
                box-shadow: var(--shadow-lg);
            }
            .continue-session-buttons {
                display: flex;
                gap: 1rem;
                justify-content: center;
                margin-top: 1rem;
            }
            @media (max-width: 480px) {
                .continue-session-buttons {
                    flex-direction: column;
                }
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Smooth scroll to target element
     * @param {string} target - The target selector
     */
    smoothScrollTo(target) {
        const element = document.querySelector(target);
        if (element) {
            element.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    }

    /**
     * Show loading overlay
     */
    showLoading() {
        if (this.loadingOverlay) {
            this.loadingOverlay.classList.remove('hidden');
        }
    }

    /**
     * Hide loading overlay
     */
    hideLoading() {
        if (this.loadingOverlay) {
            this.loadingOverlay.classList.add('hidden');
        }
    }

    /**
     * Animate elements on page load
     */
    animateOnLoad() {
        // Add fade-in animation to role cards
        const roleCards = document.querySelectorAll('.role-card');
        roleCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.6s ease-out';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 200 + (index * 150));
        });

        // Add fade-in animation to hero content
        const heroTitle = document.querySelector('.hero-title');
        const heroSubtitle = document.querySelector('.hero-subtitle');
        
        if (heroTitle) {
            heroTitle.style.opacity = '0';
            heroTitle.style.transform = 'translateY(20px)';
            setTimeout(() => {
                heroTitle.style.transition = 'all 0.6s ease-out';
                heroTitle.style.opacity = '1';
                heroTitle.style.transform = 'translateY(0)';
            }, 100);
        }

        if (heroSubtitle) {
            heroSubtitle.style.opacity = '0';
            heroSubtitle.style.transform = 'translateY(20px)';
            setTimeout(() => {
                heroSubtitle.style.transition = 'all 0.6s ease-out';
                heroSubtitle.style.opacity = '1';
                heroSubtitle.style.transform = 'translateY(0)';
            }, 300);
        }
    }

    /**
     * Utility method to get URL parameters
     * @param {string} param - Parameter name
     * @returns {string|null} Parameter value
     */
    getUrlParameter(param) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(param);
    }

    /**
     * Utility method to format dates
     * @param {Date} date - Date to format
     * @returns {string} Formatted date string
     */
    formatDate(date) {
        return new Intl.DateTimeFormat('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        }).format(date);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new App();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = App;
}
