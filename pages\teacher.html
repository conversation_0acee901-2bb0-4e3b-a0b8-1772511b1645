<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teacher Portal - ENGLISH 2025</title>
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/teacher.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="portal-header">
            <div class="container">
                <div class="header-content">
                    <div class="logo">
                        <h1>ENGLISH 2025</h1>
                        <span class="portal-type">Teacher Portal</span>
                    </div>
                    <nav class="portal-nav">
                        <a href="#dashboard" class="nav-link active">Dashboard</a>
                        <a href="#schedule" class="nav-link">Schedule</a>
                        <a href="#students" class="nav-link">Students</a>
                        <a href="#feedback" class="nav-link">Feedback</a>
                        <a href="#resources" class="nav-link">Resources</a>
                    </nav>
                    <div class="user-menu">
                        <div class="user-avatar">
                            <img src="https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=387&q=80" alt="Teacher Avatar">
                        </div>
                        <span class="user-name">Sarah Johnson</span>
                        <button class="logout-btn" onclick="logout()">Logout</button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <div class="container">
                <!-- Welcome Section -->
                <section class="welcome-section">
                    <div class="welcome-content">
                        <h2>Good morning, Sarah!</h2>
                        <p>You have <strong>5 classes</strong> scheduled today and <strong>23 active students</strong> in your roster.</p>
                    </div>
                    <div class="teacher-stats">
                        <div class="stat-card">
                            <div class="stat-number">23</div>
                            <div class="stat-label">Active Students</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">5</div>
                            <div class="stat-label">Classes Today</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">4.8</div>
                            <div class="stat-label">Avg Rating</div>
                        </div>
                    </div>
                </section>

                <!-- Dashboard Grid -->
                <div class="dashboard-grid">
                    <!-- Today's Schedule -->
                    <div class="dashboard-card schedule-card">
                        <h3>Today's Schedule</h3>
                        <div class="schedule-list">
                            <div class="schedule-item current">
                                <div class="time-slot">
                                    <div class="time">10:00 AM</div>
                                    <div class="duration">60 min</div>
                                </div>
                                <div class="class-info">
                                    <h4>Intermediate Conversation</h4>
                                    <p>Level B1 • 6 students</p>
                                    <span class="status current">In Progress</span>
                                </div>
                                <button class="btn btn-primary btn-sm">Join Class</button>
                            </div>
                            
                            <div class="schedule-item upcoming">
                                <div class="time-slot">
                                    <div class="time">2:00 PM</div>
                                    <div class="duration">60 min</div>
                                </div>
                                <div class="class-info">
                                    <h4>Travel & Tourism</h4>
                                    <p>Level B2 • 4 students</p>
                                    <span class="status upcoming">Upcoming</span>
                                </div>
                                <button class="btn btn-outline btn-sm">Prepare</button>
                            </div>
                            
                            <div class="schedule-item">
                                <div class="time-slot">
                                    <div class="time">4:00 PM</div>
                                    <div class="duration">60 min</div>
                                </div>
                                <div class="class-info">
                                    <h4>Business English</h4>
                                    <p>Level C1 • 3 students</p>
                                    <span class="status scheduled">Scheduled</span>
                                </div>
                                <button class="btn btn-outline btn-sm">View Details</button>
                            </div>
                        </div>
                    </div>

                    <!-- Student Progress Overview -->
                    <div class="dashboard-card students-card">
                        <h3>Student Progress</h3>
                        <div class="students-list">
                            <div class="student-item">
                                <div class="student-avatar">
                                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=387&q=80" alt="Student">
                                </div>
                                <div class="student-info">
                                    <h4>John Doe</h4>
                                    <p>Level B1 • Unit 3</p>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 75%"></div>
                                    </div>
                                </div>
                                <div class="student-status">
                                    <span class="status-badge active">Active</span>
                                    <span class="last-activity">2 hours ago</span>
                                </div>
                            </div>
                            
                            <div class="student-item">
                                <div class="student-avatar">
                                    <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=387&q=80" alt="Student">
                                </div>
                                <div class="student-info">
                                    <h4>Maria Garcia</h4>
                                    <p>Level A2 • Unit 2</p>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 45%"></div>
                                    </div>
                                </div>
                                <div class="student-status">
                                    <span class="status-badge needs-attention">Needs Attention</span>
                                    <span class="last-activity">1 day ago</span>
                                </div>
                            </div>
                            
                            <div class="student-item">
                                <div class="student-avatar">
                                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=387&q=80" alt="Student">
                                </div>
                                <div class="student-info">
                                    <h4>Ahmed Hassan</h4>
                                    <p>Level B2 • Unit 4</p>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 90%"></div>
                                    </div>
                                </div>
                                <div class="student-status">
                                    <span class="status-badge excellent">Excellent</span>
                                    <span class="last-activity">30 min ago</span>
                                </div>
                            </div>
                        </div>
                        <button class="btn btn-outline">View All Students</button>
                    </div>

                    <!-- Recent Feedback -->
                    <div class="dashboard-card feedback-card">
                        <h3>Recent Feedback</h3>
                        <div class="feedback-list">
                            <div class="feedback-item">
                                <div class="feedback-header">
                                    <h4>John Doe</h4>
                                    <span class="feedback-date">2 hours ago</span>
                                </div>
                                <p class="feedback-text">"Great improvement in pronunciation! Keep practicing the 'th' sound."</p>
                                <div class="feedback-rating">
                                    <span>Rating: </span>
                                    <div class="stars">★★★★☆</div>
                                </div>
                            </div>
                            
                            <div class="feedback-item">
                                <div class="feedback-header">
                                    <h4>Maria Garcia</h4>
                                    <span class="feedback-date">1 day ago</span>
                                </div>
                                <p class="feedback-text">"Excellent participation in today's conversation class. Work on past tense usage."</p>
                                <div class="feedback-rating">
                                    <span>Rating: </span>
                                    <div class="stars">★★★★★</div>
                                </div>
                            </div>
                        </div>
                        <button class="btn btn-secondary">Add New Feedback</button>
                    </div>

                    <!-- Teaching Resources -->
                    <div class="dashboard-card resources-card">
                        <h3>Teaching Resources</h3>
                        <div class="resources-grid">
                            <div class="resource-item">
                                <div class="resource-icon">📚</div>
                                <h4>Lesson Plans</h4>
                                <p>Access curriculum and lesson materials</p>
                            </div>
                            <div class="resource-item">
                                <div class="resource-icon">🎯</div>
                                <h4>Assessment Tools</h4>
                                <p>Create and manage student assessments</p>
                            </div>
                            <div class="resource-item">
                                <div class="resource-icon">📊</div>
                                <h4>Analytics</h4>
                                <p>View detailed student progress reports</p>
                            </div>
                            <div class="resource-item">
                                <div class="resource-icon">💬</div>
                                <h4>Communication</h4>
                                <p>Message students and parents</p>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="dashboard-card quick-actions">
                        <h3>Quick Actions</h3>
                        <div class="actions-list">
                            <button class="action-btn">
                                <span class="action-icon">📅</span>
                                <span>Schedule Makeup Class</span>
                            </button>
                            <button class="action-btn">
                                <span class="action-icon">📝</span>
                                <span>Create Assignment</span>
                            </button>
                            <button class="action-btn">
                                <span class="action-icon">📧</span>
                                <span>Send Announcement</span>
                            </button>
                            <button class="action-btn">
                                <span class="action-icon">📈</span>
                                <span>Generate Report</span>
                            </button>
                        </div>
                    </div>

                    <!-- Class Performance -->
                    <div class="dashboard-card performance-card">
                        <h3>This Week's Performance</h3>
                        <div class="performance-stats">
                            <div class="performance-item">
                                <div class="performance-label">Classes Taught</div>
                                <div class="performance-value">18</div>
                            </div>
                            <div class="performance-item">
                                <div class="performance-label">Student Attendance</div>
                                <div class="performance-value">92%</div>
                            </div>
                            <div class="performance-item">
                                <div class="performance-label">Average Rating</div>
                                <div class="performance-value">4.8/5</div>
                            </div>
                            <div class="performance-item">
                                <div class="performance-label">Feedback Given</div>
                                <div class="performance-value">45</div>
                            </div>
                        </div>
                        <div class="performance-chart">
                            <div class="chart-placeholder">
                                📊 Weekly Performance Chart
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="../js/data/storage.js"></script>
    <script src="../js/data/mockData.js"></script>
    <script src="../js/auth.js"></script>
    <script src="../js/teacher/teacherApp.js"></script>
    <script>
        function logout() {
            if (window.authManager) {
                window.authManager.logout();
            }
            window.location.href = '../index.html';
        }

        // Initialize teacher app
        const teacherApp = new TeacherApp();
    </script>
</body>
</html>
