/**
 * ENGLISH 2025 - Course Navigation Styles
 * Styles for course hierarchy, lesson navigation, and progress tracking
 */

/* Course Header */
.course-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-8);
  padding: var(--spacing-8);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--white);
  border-radius: var(--radius-xl);
  position: relative;
  overflow: hidden;
}

.course-header::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 300px;
  height: 300px;
  background: url('https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80') center/cover;
  opacity: 0.1;
  border-radius: 50%;
  transform: translate(30%, -30%);
}

.course-header .header-content {
  z-index: 1;
}

.course-header h1 {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-2);
  font-weight: 700;
}

.course-header p {
  font-size: var(--font-size-lg);
  opacity: 0.9;
  margin: 0;
}

.current-level-badge {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  padding: var(--spacing-4);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
  z-index: 1;
}

.level-text {
  font-size: var(--font-size-sm);
  opacity: 0.8;
  margin-bottom: var(--spacing-1);
}

.level-value {
  font-size: var(--font-size-3xl);
  font-weight: 700;
}

/* Level Navigation */
.level-navigation {
  margin-bottom: var(--spacing-8);
}

.level-tabs {
  display: flex;
  gap: var(--spacing-2);
  background: var(--gray-100);
  padding: var(--spacing-2);
  border-radius: var(--radius-lg);
  overflow-x: auto;
}

.level-tab {
  flex: 1;
  min-width: 120px;
  padding: var(--spacing-3) var(--spacing-4);
  border: none;
  background: transparent;
  color: var(--gray-600);
  font-weight: 500;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-normal);
  white-space: nowrap;
}

.level-tab:hover:not(.locked) {
  background: var(--white);
  color: var(--gray-900);
}

.level-tab.active {
  background: var(--primary-color);
  color: var(--white);
  box-shadow: var(--shadow-sm);
}

.level-tab.locked {
  color: var(--gray-400);
  cursor: not-allowed;
  position: relative;
}

.level-tab.locked::after {
  content: '🔒';
  margin-left: var(--spacing-2);
}

/* Course Content */
.course-content {
  margin-bottom: var(--spacing-8);
}

.level-info {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.level-info h2 {
  font-size: var(--font-size-3xl);
  color: var(--gray-900);
  margin-bottom: var(--spacing-3);
}

.level-info p {
  font-size: var(--font-size-lg);
  color: var(--gray-600);
  max-width: 600px;
  margin: 0 auto;
}

/* Units Grid */
.units-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-6);
}

.unit-card {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.unit-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.unit-card.locked {
  opacity: 0.6;
  cursor: not-allowed;
}

.unit-card.locked:hover {
  transform: none;
  box-shadow: var(--shadow-md);
}

.unit-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-4);
}

.unit-number {
  background: var(--primary-color);
  color: var(--white);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: var(--font-size-lg);
}

.unit-status {
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.unit-status.completed {
  background: var(--success-light);
  color: var(--success-dark);
}

.unit-status.in-progress {
  background: var(--warning-light);
  color: var(--warning-dark);
}

.unit-status.locked {
  background: var(--gray-200);
  color: var(--gray-600);
}

.unit-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-2);
}

.unit-description {
  color: var(--gray-600);
  margin-bottom: var(--spacing-4);
  line-height: 1.5;
}

.unit-progress {
  margin-bottom: var(--spacing-4);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2);
}

.progress-header span:first-child {
  font-weight: 500;
  color: var(--gray-700);
}

.progress-header span:last-child {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.progress-bar {
  height: 8px;
  background: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--radius-full);
  transition: width 1s ease-out;
}

.unit-topics {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
}

.topic-tag {
  background: var(--gray-100);
  color: var(--gray-700);
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal-content {
  background: var(--white);
  border-radius: var(--radius-xl);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: var(--shadow-xl);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--gray-200);
}

.modal-header h3 {
  margin: 0;
  font-size: var(--font-size-xl);
  color: var(--gray-900);
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-2xl);
  color: var(--gray-500);
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all var(--transition-normal);
}

.modal-close:hover {
  background: var(--gray-100);
  color: var(--gray-700);
}

.modal-body {
  padding: var(--spacing-6);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-3);
  padding: var(--spacing-6);
  border-top: 1px solid var(--gray-200);
}

/* Lessons List */
.lessons-list {
  margin-top: var(--spacing-6);
}

.lesson-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-4);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-3);
  transition: all var(--transition-normal);
  cursor: pointer;
}

.lesson-item:hover:not(.locked) {
  border-color: var(--primary-color);
  background: var(--primary-light);
}

.lesson-item.completed {
  background: var(--success-light);
  border-color: var(--success-color);
}

.lesson-item.locked {
  opacity: 0.6;
  cursor: not-allowed;
}

.lesson-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--gray-200);
  color: var(--gray-600);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: var(--spacing-3);
}

.lesson-item.completed .lesson-number {
  background: var(--success-color);
  color: var(--white);
}

.lesson-item.current .lesson-number {
  background: var(--primary-color);
  color: var(--white);
}

.lesson-content {
  flex: 1;
}

.lesson-title {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
}

.lesson-type {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.lesson-status {
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.lesson-status.completed {
  color: var(--success-dark);
}

.lesson-status.current {
  color: var(--primary-color);
}

.lesson-status.locked {
  color: var(--gray-500);
}

/* Unit Exam Section */
.unit-exam {
  margin-top: var(--spacing-6);
  padding: var(--spacing-4);
  background: var(--accent-light);
  border-radius: var(--radius-lg);
  border: 2px solid var(--accent-color);
}

.exam-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-3);
}

.exam-icon {
  font-size: var(--font-size-xl);
  margin-right: var(--spacing-2);
}

.exam-title {
  font-weight: 600;
  color: var(--gray-900);
}

.exam-description {
  color: var(--gray-700);
  margin-bottom: var(--spacing-4);
}

.exam-requirements {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  margin-bottom: var(--spacing-4);
}

/* Responsive Design */
@media (max-width: 768px) {
  .course-header {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-4);
  }

  .level-tabs {
    flex-wrap: wrap;
  }

  .units-grid {
    grid-template-columns: 1fr;
  }

  .modal-content {
    width: 95%;
    margin: var(--spacing-4);
  }

  .lesson-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }
}
