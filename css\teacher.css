/* Teacher Portal Specific Styles */

/* Portal Header */
.portal-header {
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
  color: var(--white);
  box-shadow: var(--shadow-lg);
}

.portal-header .logo h1 {
  color: var(--white);
  margin-bottom: var(--spacing-1);
}

.portal-type {
  font-size: var(--font-size-sm);
  opacity: 0.9;
}

.portal-nav {
  display: flex;
  gap: var(--spacing-6);
}

.portal-nav .nav-link {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.portal-nav .nav-link:hover,
.portal-nav .nav-link.active {
  color: var(--white);
  background-color: rgba(255, 255, 255, 0.1);
}

.user-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.user-avatar img {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.user-name {
  font-weight: 500;
  color: var(--white);
}

.logout-btn {
  background: rgba(255, 255, 255, 0.1);
  color: var(--white);
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Main Content */
.main-content {
  padding: var(--spacing-8) 0;
  min-height: calc(100vh - 80px);
}

/* Welcome Section */
.welcome-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-8);
  padding: var(--spacing-6);
  background: linear-gradient(135deg, var(--white), var(--gray-50));
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
}

.welcome-content h2 {
  font-size: var(--font-size-3xl);
  color: var(--gray-900);
  margin-bottom: var(--spacing-2);
}

.welcome-content p {
  color: var(--gray-600);
  font-size: var(--font-size-lg);
  margin-bottom: 0;
}

.teacher-stats {
  display: flex;
  gap: var(--spacing-4);
}

.stat-card {
  text-align: center;
  padding: var(--spacing-4);
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  min-width: 100px;
}

.stat-number {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--secondary-color);
  margin-bottom: var(--spacing-1);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

/* Dashboard Grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-6);
}

.dashboard-card {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.dashboard-card h3 {
  font-size: var(--font-size-xl);
  color: var(--gray-900);
  margin-bottom: var(--spacing-4);
  border-bottom: 2px solid var(--gray-100);
  padding-bottom: var(--spacing-2);
}

/* Schedule Card */
.schedule-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-4);
}

.schedule-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
  border-radius: var(--radius-lg);
  border: 2px solid var(--gray-100);
  transition: all var(--transition-fast);
}

.schedule-item.current {
  border-color: var(--secondary-color);
  background: rgba(16, 185, 129, 0.05);
}

.schedule-item.upcoming {
  border-color: var(--accent-color);
  background: rgba(245, 158, 11, 0.05);
}

.time-slot {
  text-align: center;
  min-width: 80px;
}

.time {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-900);
}

.duration {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.class-info {
  flex: 1;
}

.class-info h4 {
  font-size: var(--font-size-lg);
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
}

.class-info p {
  color: var(--gray-600);
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-sm);
}

.status {
  display: inline-block;
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
}

.status.current {
  background: var(--secondary-color);
  color: var(--white);
}

.status.upcoming {
  background: var(--accent-color);
  color: var(--white);
}

.status.scheduled {
  background: var(--gray-200);
  color: var(--gray-700);
}

.btn-sm {
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-sm);
  min-height: 36px;
}

/* Students Card */
.students-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-4);
}

.student-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  border-radius: var(--radius-lg);
  background: var(--gray-50);
}

.student-avatar img {
  width: 50px;
  height: 50px;
  border-radius: var(--radius-full);
  border: 2px solid var(--gray-200);
}

.student-info {
  flex: 1;
}

.student-info h4 {
  font-size: var(--font-size-base);
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
}

.student-info p {
  color: var(--gray-600);
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-sm);
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
  border-radius: var(--radius-full);
  transition: width var(--transition-normal);
}

.student-status {
  text-align: right;
}

.status-badge {
  display: block;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: 600;
  margin-bottom: var(--spacing-1);
}

.status-badge.active {
  background: var(--secondary-color);
  color: var(--white);
}

.status-badge.needs-attention {
  background: var(--warning-color);
  color: var(--white);
}

.status-badge.excellent {
  background: var(--success-color);
  color: var(--white);
}

.last-activity {
  font-size: var(--font-size-xs);
  color: var(--gray-500);
}

/* Feedback Card */
.feedback-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-4);
}

.feedback-item {
  padding: var(--spacing-4);
  border-radius: var(--radius-lg);
  background: var(--gray-50);
}

.feedback-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2);
}

.feedback-header h4 {
  font-size: var(--font-size-base);
  color: var(--gray-900);
  margin: 0;
}

.feedback-date {
  font-size: var(--font-size-xs);
  color: var(--gray-500);
}

.feedback-text {
  color: var(--gray-700);
  margin-bottom: var(--spacing-2);
  font-style: italic;
}

.feedback-rating {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
}

.stars {
  color: var(--accent-color);
}

/* Resources Card */
.resources-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-3);
}

.resource-item {
  text-align: center;
  padding: var(--spacing-4);
  border-radius: var(--radius-lg);
  background: var(--gray-50);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.resource-item:hover {
  background: var(--secondary-color);
  color: var(--white);
  transform: translateY(-2px);
}

.resource-icon {
  font-size: var(--font-size-2xl);
  margin-bottom: var(--spacing-2);
}

.resource-item h4 {
  font-size: var(--font-size-base);
  margin-bottom: var(--spacing-1);
}

.resource-item p {
  font-size: var(--font-size-sm);
  margin: 0;
  opacity: 0.8;
}

/* Quick Actions Card */
.actions-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.action-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  background: var(--gray-50);
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  color: var(--gray-700);
  text-align: left;
  width: 100%;
}

.action-btn:hover {
  background: var(--secondary-color);
  color: var(--white);
  transform: translateX(4px);
}

.action-icon {
  font-size: var(--font-size-lg);
  width: 24px;
  text-align: center;
}

/* Performance Card */
.performance-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.performance-item {
  text-align: center;
  padding: var(--spacing-3);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
}

.performance-label {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  margin-bottom: var(--spacing-1);
}

.performance-value {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--secondary-color);
}

.performance-chart {
  text-align: center;
  padding: var(--spacing-8);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
  color: var(--gray-500);
}

.chart-placeholder {
  font-size: var(--font-size-lg);
}

/* Responsive Design */
@media (max-width: 768px) {
  .welcome-section {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-4);
  }
  
  .teacher-stats {
    justify-content: center;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .portal-nav {
    display: none;
  }
  
  .user-menu {
    gap: var(--spacing-2);
  }
  
  .user-name {
    display: none;
  }
  
  .schedule-item {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-2);
  }
  
  .resources-grid {
    grid-template-columns: 1fr;
  }
  
  .performance-stats {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .student-item {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-2);
  }
  
  .feedback-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-1);
  }
}
