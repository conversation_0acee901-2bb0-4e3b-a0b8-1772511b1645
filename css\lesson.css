/**
 * ENGLISH 2025 - <PERSON>on Player Styles
 * Styles for interactive lesson content and exercises
 */

/* <PERSON>on Header */
.lesson-header {
  background: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--spacing-4) 0;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: var(--shadow-sm);
}

.lesson-header .header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.lesson-nav {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.back-btn {
  background: none;
  border: 1px solid var(--gray-300);
  color: var(--gray-700);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-normal);
  font-weight: 500;
}

.back-btn:hover {
  background: var(--gray-50);
  border-color: var(--gray-400);
}

.lesson-info h1 {
  font-size: var(--font-size-xl);
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
}

.lesson-info p {
  color: var(--gray-600);
  margin: 0;
}

.lesson-progress {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  min-width: 200px;
}

.lesson-progress .progress-bar {
  flex: 1;
  height: 8px;
  background: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.lesson-progress .progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--radius-full);
  transition: width 0.5s ease-out;
}

#progress-text {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  font-weight: 500;
  white-space: nowrap;
}

/* Lesson Content */
.lesson-content {
  padding: var(--spacing-8) 0;
  min-height: calc(100vh - 200px);
}

.lesson-step {
  display: none;
  animation: fadeIn 0.5s ease-out;
}

.lesson-step.active {
  display: block;
}

.step-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.step-header h2 {
  font-size: var(--font-size-3xl);
  color: var(--gray-900);
  margin-bottom: var(--spacing-3);
}

.step-header p {
  font-size: var(--font-size-lg);
  color: var(--gray-600);
}

/* Vocabulary Section */
.vocabulary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-6);
  max-width: 1000px;
  margin: 0 auto;
}

.vocab-card {
  background: var(--white);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.vocab-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.vocab-image {
  height: 200px;
  overflow: hidden;
}

.vocab-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.vocab-content {
  padding: var(--spacing-6);
  text-align: center;
}

.vocab-content h3 {
  font-size: var(--font-size-xl);
  color: var(--gray-900);
  margin-bottom: var(--spacing-2);
}

.pronunciation {
  font-size: var(--font-size-base);
  color: var(--primary-color);
  font-style: italic;
  margin-bottom: var(--spacing-3);
}

.play-audio {
  background: var(--primary-color);
  color: var(--white);
  border: none;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  font-size: var(--font-size-xl);
  cursor: pointer;
  transition: all var(--transition-normal);
  margin-bottom: var(--spacing-3);
}

.play-audio:hover {
  background: var(--primary-dark);
  transform: scale(1.1);
}

.definition {
  color: var(--gray-600);
  line-height: 1.5;
  margin: 0;
}

/* Dialogue Section */
.dialogue-container {
  max-width: 800px;
  margin: 0 auto;
}

.dialogue-controls {
  display: flex;
  justify-content: center;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.play-dialogue,
.repeat-dialogue {
  background: var(--secondary-color);
  color: var(--white);
  border: none;
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-lg);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.play-dialogue:hover,
.repeat-dialogue:hover {
  background: var(--secondary-dark);
  transform: translateY(-2px);
}

.dialogue-script {
  background: var(--gray-50);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
}

.dialogue-line {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
  margin-bottom: var(--spacing-3);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
}

.dialogue-line:hover {
  background: var(--white);
}

.dialogue-line.speaker-a {
  background: var(--primary-light);
}

.dialogue-line.speaker-b {
  background: var(--secondary-light);
  flex-direction: row-reverse;
}

.speaker {
  font-weight: 600;
  color: var(--gray-900);
  min-width: 80px;
}

.text {
  flex: 1;
  color: var(--gray-800);
  line-height: 1.5;
}

.play-line {
  background: none;
  border: none;
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: 50%;
  transition: all var(--transition-normal);
}

.play-line:hover {
  background: rgba(0, 0, 0, 0.1);
}

/* Grammar Section */
.grammar-content {
  max-width: 800px;
  margin: 0 auto;
}

.grammar-rule {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-md);
}

.grammar-rule h3 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-2xl);
}

.grammar-rule > p {
  color: var(--gray-700);
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-6);
  line-height: 1.6;
}

.grammar-examples,
.grammar-structure {
  margin-bottom: var(--spacing-6);
}

.grammar-examples h4,
.grammar-structure h4 {
  color: var(--gray-900);
  margin-bottom: var(--spacing-3);
}

.grammar-examples ul {
  list-style: none;
  padding: 0;
}

.grammar-examples li {
  background: var(--gray-50);
  padding: var(--spacing-3);
  margin-bottom: var(--spacing-2);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--accent-color);
}

.structure-box {
  background: var(--primary-light);
  padding: var(--spacing-4);
  border-radius: var(--radius-lg);
  border: 2px solid var(--primary-color);
}

.structure-box p {
  margin: 0;
  color: var(--primary-dark);
  font-weight: 500;
}

/* Exercise Section */
.exercise-container {
  max-width: 800px;
  margin: 0 auto;
}

.exercise-question {
  text-align: center;
  margin-bottom: var(--spacing-6);
}

.exercise-question h3 {
  color: var(--gray-900);
  margin-bottom: var(--spacing-3);
}

.target-sentence {
  background: var(--gray-100);
  padding: var(--spacing-3);
  border-radius: var(--radius-md);
  font-style: italic;
  color: var(--gray-600);
  display: none; /* Hidden until exercise is completed */
}

.word-bank {
  margin-bottom: var(--spacing-6);
}

.word-options {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-3);
  justify-content: center;
}

.word-option {
  background: var(--white);
  border: 2px solid var(--gray-300);
  color: var(--gray-700);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-normal);
  font-weight: 500;
}

.word-option:hover {
  border-color: var(--primary-color);
  background: var(--primary-light);
}

.word-option.selected {
  background: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.word-option:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.sentence-builder {
  background: var(--gray-50);
  border: 2px dashed var(--gray-300);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-6);
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.built-sentence {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
  justify-content: center;
  min-height: 40px;
  align-items: center;
}

.built-word {
  background: var(--primary-color);
  color: var(--white);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.built-word:hover {
  background: var(--primary-dark);
}

.clear-sentence {
  position: absolute;
  top: var(--spacing-2);
  right: var(--spacing-2);
  background: var(--gray-400);
  color: var(--white);
  border: none;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.clear-sentence:hover {
  background: var(--gray-600);
}

.exercise-actions {
  text-align: center;
}

.check-answer {
  background: var(--accent-color);
  color: var(--white);
  border: none;
  padding: var(--spacing-3) var(--spacing-8);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-lg);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.check-answer:hover {
  background: var(--accent-dark);
  transform: translateY(-2px);
}

.exercise-feedback {
  margin-top: var(--spacing-4);
  padding: var(--spacing-3);
  border-radius: var(--radius-md);
  font-weight: 500;
  text-align: center;
}

.exercise-feedback.correct {
  background: var(--success-light);
  color: var(--success-dark);
  border: 1px solid var(--success-color);
}

.exercise-feedback.incorrect {
  background: var(--error-light);
  color: var(--error-dark);
  border: 1px solid var(--error-color);
}

/* Lesson Footer */
.lesson-footer {
  background: var(--white);
  border-top: 1px solid var(--gray-200);
  padding: var(--spacing-4) 0;
  position: sticky;
  bottom: 0;
  z-index: 100;
}

.lesson-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-btn {
  background: var(--primary-color);
  color: var(--white);
  border: none;
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-lg);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.nav-btn:hover:not(:disabled) {
  background: var(--primary-dark);
  transform: translateY(-2px);
}

.nav-btn:disabled {
  background: var(--gray-300);
  color: var(--gray-500);
  cursor: not-allowed;
  transform: none;
}

.step-indicators {
  display: flex;
  gap: var(--spacing-2);
}

.step-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--gray-300);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.step-dot.active {
  background: var(--primary-color);
  transform: scale(1.2);
}

.step-dot.completed {
  background: var(--success-color);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .lesson-header .header-content {
    flex-direction: column;
    gap: var(--spacing-3);
  }

  .lesson-nav {
    width: 100%;
    justify-content: space-between;
  }

  .lesson-progress {
    width: 100%;
    min-width: auto;
  }

  .vocabulary-grid {
    grid-template-columns: 1fr;
  }

  .dialogue-line {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-2);
  }

  .dialogue-line.speaker-b {
    flex-direction: column;
  }

  .word-options {
    gap: var(--spacing-2);
  }

  .word-option {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-sm);
  }

  .lesson-navigation {
    flex-direction: column;
    gap: var(--spacing-3);
  }

  .nav-btn {
    width: 100%;
  }
}
