<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Courses - ENGLISH 2025</title>
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/student.css">
    <link rel="stylesheet" href="../css/courses.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="portal-header">
            <div class="container">
                <div class="header-content">
                    <div class="logo">
                        <h1>ENGLISH 2025</h1>
                        <span class="portal-type">Course Catalog</span>
                    </div>
                    <nav class="portal-nav">
                        <a href="student.html" class="nav-link">Dashboard</a>
                        <a href="courses.html" class="nav-link active">Courses</a>
                        <a href="#schedule" class="nav-link">Schedule</a>
                        <a href="#ai-tutor" class="nav-link">AI Tutor</a>
                        <a href="#progress" class="nav-link">Progress</a>
                    </nav>
                    <div class="user-menu">
                        <div class="user-avatar">
                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=387&q=80" alt="Student Avatar">
                        </div>
                        <span class="user-name">John Doe</span>
                        <button class="logout-btn" onclick="logout()">Logout</button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <div class="container">
                <!-- Course Header -->
                <section class="course-header">
                    <div class="header-content">
                        <h1>Your Learning Path</h1>
                        <p>Progress through structured English levels from beginner to advanced</p>
                    </div>
                    <div class="current-level-badge">
                        <span class="level-text">Current Level</span>
                        <span class="level-value">B1</span>
                    </div>
                </section>

                <!-- Level Navigation -->
                <section class="level-navigation">
                    <div class="level-tabs">
                        <button class="level-tab" data-level="A1">A1 Beginner</button>
                        <button class="level-tab" data-level="A2">A2 Elementary</button>
                        <button class="level-tab active" data-level="B1">B1 Intermediate</button>
                        <button class="level-tab locked" data-level="B2">B2 Upper Int.</button>
                        <button class="level-tab locked" data-level="C1">C1 Advanced</button>
                    </div>
                </section>

                <!-- Course Content -->
                <section class="course-content">
                    <div class="level-info">
                        <h2 id="level-title">B1 - Lower Intermediate</h2>
                        <p id="level-description">Build confidence in everyday conversations and expand your vocabulary for work and travel situations.</p>
                    </div>

                    <!-- Units Grid -->
                    <div class="units-grid" id="units-container">
                        <!-- Units will be dynamically loaded here -->
                    </div>
                </section>

                <!-- Unit Details Modal -->
                <div class="modal-overlay" id="unit-modal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 id="modal-unit-title">Unit Title</h3>
                            <button class="modal-close" onclick="closeUnitModal()">&times;</button>
                        </div>
                        <div class="modal-body">
                            <div class="unit-progress">
                                <div class="progress-header">
                                    <span>Progress</span>
                                    <span id="unit-progress-text">0/4 lessons</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" id="unit-progress-bar"></div>
                                </div>
                            </div>
                            <div class="lessons-list" id="lessons-container">
                                <!-- Lessons will be dynamically loaded here -->
                            </div>
                            <div class="unit-exam" id="unit-exam-section">
                                <!-- Unit exam section -->
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn btn-secondary" onclick="closeUnitModal()">Close</button>
                            <button class="btn btn-primary" id="continue-unit-btn">Continue Learning</button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="../js/data/storage.js"></script>
    <script src="../js/data/mockData.js"></script>
    <script src="../js/data/dataManager.js"></script>
    <script src="../js/auth.js"></script>
    <script src="../js/components/courseNavigator.js"></script>
    <script>
        function logout() {
            if (window.authManager) {
                window.authManager.logout();
            }
            window.location.href = '../index.html';
        }

        function closeUnitModal() {
            document.getElementById('unit-modal').style.display = 'none';
        }

        // Initialize course navigator
        document.addEventListener('DOMContentLoaded', function() {
            if (window.authManager && window.authManager.isAuthenticated()) {
                window.courseNavigator = new CourseNavigator();
            } else {
                window.location.href = '../index.html';
            }
        });
    </script>
</body>
</html>
